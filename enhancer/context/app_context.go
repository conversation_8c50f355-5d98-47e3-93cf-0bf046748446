package context

import (
	"regexp"
	"strings"

	"github.com/precize/common"
	"github.com/precize/elastic"
)

func GetAppNameFromValue(str string, opts ...ContextCriteriaOptions) (app string) {

	var (
		matched    string
		matchedLen int
		criteria   ContextCriteria
	)

	for _, opt := range opts {
		opt(&criteria)
	}

	str = strings.ToLower(str)

	for appName, values := range appValues {
		for _, val := range values {
			if strings.Contains(val, `\b`) {
				// \b whole word match does not consider _ as special character
				str = strings.ReplaceAll(str, "_", "-")
			}

			regex := regexp.MustCompile(val)
			if regex.MatchString(str) && len(appName) > matchedLen {
				if !unsupportedApp(appName, criteria) {
					matchedLen = len(appName)
					matched = appName
				}
			}
		}
	}

	if len(matched) > 0 {
		app = matched
	}

	return
}

func GetAppNameListFromValue(str string, opts ...ContextCriteriaOptions) []string {

	var (
		appNames = make([]string, 0)
		criteria ContextCriteria
	)

	str = strings.ToLower(str)

	for _, opt := range opts {
		opt(&criteria)
	}

	for app, values := range appValues {
		for _, val := range values {
			if strings.Contains(val, `\b`) {
				// \b whole word match does not consider _ as special character
				str = strings.ReplaceAll(str, "_", "-")
			}

			regex := regexp.MustCompile(val)
			if regex.MatchString(str) {
				if !unsupportedApp(app, criteria) {
					appNames = append(appNames, app)
				}
			}
		}
	}

	return appNames
}

func GetUniqueAppContext(resourceContextDoc *common.ResourceContextInsertDoc) (app []string) {

	uniqueApp := make(map[string]struct{})

	getUniqueContext(&resourceContextDoc.DefinedApp, uniqueApp)
	getUniqueContext(&resourceContextDoc.DerivedApp, uniqueApp)

	for appName := range uniqueApp {
		app = append(app, appName)
	}

	return
}

func AddApplicationToGlobalApps(application string) {

	globalValuesMutex.Lock()

	var globallyExists bool

	for _, defaultAppValue := range defaultAppValues {
		subApps := strings.Split(defaultAppValue, ",")
		for _, subApp := range subApps {
			if strings.ToLower(subApp) == strings.ToLower(application) {
				globallyExists = true
				break
			}
		}

		if globallyExists {
			break
		}
	}

	if !globallyExists {
		defaultAppValues = append(defaultAppValues, application)
	}

	globalValuesMutex.Unlock()
}

func unsupportedApp(appName string, criteria ContextCriteria) bool {

	switch appName {
	case JENKINS_APP, ARGO_APP, CICD_APP, GITHUB_APP, GITLAB_APP, BITBUCKET_APP:
		switch criteria.ResourceType {
		case common.AWS_EC2_RESOURCE_TYPE, common.GCP_INSTANCE_RESOURCE_TYPE, common.AZURE_VM_RESOURCE_TYPE:
		default:
			return true
		}
	case TERRAFORM_APP:
		switch criteria.ResourceType {
		case common.AZURE_GRAPHAPP_RESOURCE_TYPE, common.GCP_SERVICEACCOUNT_RESOURCE_TYPE, common.GCP_SAPOLICYBINDING_RESOURCE_TYPE, common.AWS_IAM_ROLE_RESOURCE_TYPE, common.AWS_IAM_USER_RESOURCE_TYPE:
		default:
			return true
		}
	}

	return false
}

func processResourceNamesForApps(resourceContext *ResourceContext, contextDocIDs []string) {

	var resourceNames = make([]string, 0, len(contextDocIDs))

	for _, contextDocID := range contextDocIDs {
		if contextDoc, ok := resourceContext.GetResourceContextInsertDoc(contextDocID); ok {

			// exclude few resource types

			switch contextDoc.ResourceType {
			case common.AWS_IAM_ROLE_RESOURCE_TYPE, common.AWS_IAM_USER_RESOURCE_TYPE, common.GCP_SERVICEACCOUNT_RESOURCE_TYPE, common.AZURE_GRAPHAPP_RESOURCE_TYPE, common.GCP_ROLE_RESOURCE_TYPE, common.AWS_IAMPOLICY_RESOURCE_TYPE:
				continue
			}

			if len(contextDoc.ResourceName) > 0 {
				resourceNames = append(resourceNames, strings.ToLower(contextDoc.ResourceName))
			}
		}
	}

	apps := common.GetAppsFromRscNames(resourceNames, resourceContext.TenantID)

	for app, rscName := range apps {

		excludeApp := false

		for _, defaultAppValue := range defaultAppValues {
			if strings.ToLower(defaultAppValue) == strings.ToLower(app) {
				excludeApp = true
				break
			}
		}

		if excludeApp {
			continue
		}

		if _, ok := appValues[app]; ok || ignoreGlobalAppAddition(app) {
			continue
		}

		app = FormatContextValue(app)
		AddApplicationToAIDetectedGlobalApps(app, rscName)
	}
}

func AddApplicationToAIDetectedGlobalApps(application, rscName string) {

	globalValuesMutex.Lock()

	if _, ok := aiDetectedApps[application]; !ok {
		aiDetectedApps[application] = rscName
	}

	globalValuesMutex.Unlock()
}

// should global app detected by ai be ignored
func ignoreGlobalAppAddition(app string) bool {
	text := "ignoreGlobalApp:::" + strings.ToLower(app)
	textLookupDocID := common.GenerateCombinedHashID(text)

	if doc, _ := elastic.GetDocument(elastic.TEXT_LOOKUP_INDEX, textLookupDocID); len(doc) > 0 {
		if ignoreApp, ok := doc["hasName"].(bool); ok {
			return ignoreApp
		}
	}

	return false
}
