package context

import (
	"encoding/json"
	"fmt"
	"slices"
	"strings"

	"github.com/precize/common"
	"github.com/precize/logger"
)

type AzureLog struct {
	Claims Claims `json:"claims"`
}

type Claims struct {
	AppID    string `json:"appid"`
	IDType   string `json:"idtyp"`
	ObjectID string `json:"http://schemas.microsoft.com/identity/claims/objectidentifier"`
	TenantID string `json:"http://schemas.microsoft.com/identity/claims/tenantid"`
}

func parseAzureActivity(resourceContext *ResourceContext, eventInfo EventInfo,
	activityUsers map[string]common.ResourceContextItem, resourceContextInsertDoc common.ResourceContextInsertDoc) {

	var (
		azureLog         AzureLog
		uniqueIdentities = make(map[string]struct{})
	)

	if err := json.Unmarshal([]byte(eventInfo.eventJSONString), &azureLog); err != nil {
		logger.Print(logger.ERROR, "Got error unmarshalling", []string{resourceContext.TenantID}, err)
		return
	}

	if azureLog.Claims.IDType == "app" {

		enterpriseAppID := eventInfo.username
		azureTenantID := azureLog.Claims.TenantID

		getApplicationOwnersForActivity(resourceContext, enterpriseAppID, azureTenantID, eventInfo, activityUsers, uniqueIdentities, "User owned Application "+enterpriseAppID+" has performed activities on the resource", "a", resourceContextInsertDoc)

		tenantContextID := common.GenerateCombinedHashID(azureTenantID, common.AZURE_TENANT_RESOURCE_TYPE, azureTenantID, resourceContext.LastCollectedAt, resourceContext.TenantID)

		var originString = "External "

		if _, ok := resourceContext.GetResourceContextInsertDoc(tenantContextID); ok {
			originString = "Internal "
		}

		enterpriseAppName := getAzureAppName(resourceContext, enterpriseAppID, azureTenantID)

		// Add Application as an owner of this resource via Activity
		activityUsers[eventInfo.eventTime+"a"] = resourceContext.GetUserContextItem(enterpriseAppName+APP_USER_SUFFIX, common.ACTIVITY_USER_TYPE,
			originString+"application has performed activities on the resource", enterpriseAppID, resourceContextInsertDoc.Account, &common.ResourceCtxEvent{
				Name:            eventInfo.event,
				Region:          eventInfo.region,
				Time:            eventInfo.eventTime,
				IdentityAccount: azureTenantID,
			})
	} else if slices.Contains(resourceContext.AzureTenantIDs, eventInfo.account) {
		// Azure Audit log

		enterpriseAppID := eventInfo.username
		azureTenantID := eventInfo.account

		getApplicationOwnersForActivity(resourceContext, enterpriseAppID, azureTenantID, eventInfo, activityUsers, uniqueIdentities, "User owned Application "+enterpriseAppID+" has performed activities on the resource", "a", resourceContextInsertDoc)

		enterpriseAppName := getAzureAppName(resourceContext, enterpriseAppID, azureTenantID)

		// Add Application as an owner of this resource via Activity
		activityUsers[eventInfo.eventTime+"a"] = resourceContext.GetUserContextItem(enterpriseAppName+APP_USER_SUFFIX, common.ACTIVITY_USER_TYPE,
			"Internal application has performed activities on the resource", enterpriseAppID, resourceContextInsertDoc.Account, &common.ResourceCtxEvent{
				Name:            eventInfo.event,
				Region:          eventInfo.region,
				Time:            eventInfo.eventTime,
				IdentityAccount: azureTenantID,
			})
	} else {
		logger.Print(logger.INFO, "Unhandled Azure Activity user case", []string{resourceContext.TenantID}, eventInfo.username)
	}
}

func getApplicationOwnersForActivity(resourceContext *ResourceContext, applicationID, azureTenantID string, eventInfo EventInfo, activityUsers map[string]common.ResourceContextItem, uniqueIdentities map[string]struct{}, desc, sortAlphabet string, resourceContextInsertDoc common.ResourceContextInsertDoc) {

	if _, ok := uniqueIdentities[applicationID]; ok {
		return
	}

	uniqueIdentities[applicationID] = struct{}{}
	depth := len(sortAlphabet)

	appContextID := common.GenerateCombinedHashID(applicationID, common.AZURE_GRAPHAPP_RESOURCE_TYPE, azureTenantID, resourceContext.LastCollectedAt, resourceContext.TenantID)

	if appDoc, ok := resourceContext.GetResourceContextInsertDoc(appContextID); ok {

		// Resourcename user will not be derived for the service identity at this time. Additional entry for it if exists
		applicationName := applicationID
		if len(appDoc.ResourceName) > 0 {
			applicationName = appDoc.ResourceName
		}

		activityUsers[eventInfo.eventTime+sortAlphabet+"c"] = resourceContext.GetUserContextItem(applicationName+PLACEHOLDER_USER_SUFFIX, common.ACTIVITY_USER_TYPE,
			desc, "", resourceContextInsertDoc.Account, &common.ResourceCtxEvent{
				Name:          eventInfo.event,
				Region:        eventInfo.region,
				Time:          eventInfo.eventTime,
				IndirectEvent: true,
			})

		for i, defined := range appDoc.DefinedOwners {
			if strings.HasSuffix(defined.Name, APP_USER_SUFFIX) {
				if depth <= 5 && len(defined.IdentityId) > 0 {
					// Recursive call
					getApplicationOwnersForActivity(resourceContext, defined.IdentityId, azureTenantID, eventInfo, activityUsers, uniqueIdentities, getIdentityChainDescription(desc, defined.IdentityId, depth), sortAlphabet+sortAlphabet, resourceContextInsertDoc)
				}
			} else if IsNonHumanEmail(defined.Name, resourceContext) {
				uniqueGroupEmailIdentities := make(map[string]struct{})
				getNonHumanEmailOwnersForActivity(resourceContext, defined.Name, eventInfo, activityUsers, uniqueGroupEmailIdentities, "User owned group email "+defined.Name+" has performed activities on the resource", sortAlphabet+sortAlphabet)
			} else {
				if _, ok := uniqueIdentities[defined.Name]; !ok || depth == 1 {
					uniqueIdentities[defined.Name] = struct{}{}
					activityUsers[eventInfo.eventTime+sortAlphabet+"b"+fmt.Sprintf("%03d", len(appDoc.DefinedOwners)-i)] = resourceContext.GetUserContextItem(defined.Name, common.ACTIVITY_USER_TYPE,
						desc, "", resourceContextInsertDoc.Account, &common.ResourceCtxEvent{
							Name:          eventInfo.event,
							Region:        eventInfo.region,
							Time:          eventInfo.eventTime,
							IndirectEvent: true,
						})
				}
			}
		}

		for i, derived := range appDoc.DerivedOwners {
			if strings.HasSuffix(derived.Name, APP_USER_SUFFIX) {
				if depth <= 5 && len(derived.IdentityId) > 0 {
					// Recursive call
					getApplicationOwnersForActivity(resourceContext, derived.IdentityId, azureTenantID, eventInfo, activityUsers, uniqueIdentities, getIdentityChainDescription(desc, derived.IdentityId, depth), sortAlphabet+sortAlphabet, resourceContextInsertDoc)
				}
			} else if IsNonHumanEmail(derived.Name, resourceContext) {
				uniqueGroupEmailIdentities := make(map[string]struct{})
				getNonHumanEmailOwnersForActivity(resourceContext, derived.Name, eventInfo, activityUsers, uniqueGroupEmailIdentities, "User owned group email "+derived.Name+" has performed activities on the resource", sortAlphabet+sortAlphabet)
			} else {
				if _, ok := uniqueIdentities[derived.Name]; !ok || depth == 1 {
					uniqueIdentities[derived.Name] = struct{}{}
					activityUsers[eventInfo.eventTime+sortAlphabet+"a"+fmt.Sprintf("%03d", len(appDoc.DerivedOwners)-i)] = resourceContext.GetUserContextItem(derived.Name, common.ACTIVITY_USER_TYPE,
						desc, "", resourceContextInsertDoc.Account, &common.ResourceCtxEvent{
							Name:          eventInfo.event,
							Region:        eventInfo.region,
							Time:          eventInfo.eventTime,
							IndirectEvent: true,
						})
				}
			}
		}
	}
}

func getApplicationOwnersForPolicy(resourceContext *ResourceContext, application, azureTenantID string,
	resourceContextInsertDoc *common.ResourceContextInsertDoc, uniqueIdentities map[string]struct{}, desc string, depth int) {

	if _, ok := uniqueIdentities[application]; ok {
		return
	}

	uniqueIdentities[application] = struct{}{}

	appContextID := common.GenerateCombinedHashID(application, common.AZURE_GRAPHAPP_RESOURCE_TYPE, azureTenantID, resourceContext.LastCollectedAt, resourceContext.TenantID)

	if appDoc, ok := resourceContext.GetResourceContextInsertDoc(appContextID); ok {

		// Resourcename user will not be derived for the service identity at this time. Additional entry for it if exists
		applicationName := application
		if len(appDoc.ResourceName) > 0 {
			applicationName = appDoc.ResourceName
		}

		resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners = append(
			resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners, resourceContext.GetUserContextItem(applicationName+PLACEHOLDER_USER_SUFFIX, common.POLICYBINDING_USER_TYPE,
				desc, "", resourceContextInsertDoc.Account, nil))

		for _, defined := range appDoc.DefinedOwners {
			if strings.HasSuffix(defined.Name, APP_USER_SUFFIX) {
				if depth <= 5 && len(defined.IdentityId) > 0 {
					// Recursive call
					getApplicationOwnersForPolicy(resourceContext, defined.IdentityId, azureTenantID, resourceContextInsertDoc, uniqueIdentities, getIdentityChainDescription(desc, defined.IdentityId, depth), depth+1)
				}
			} else if IsNonHumanEmail(defined.Name, resourceContext) {
				uniqueGroupEmailIdentities := make(map[string]struct{})
				getNonHumanEmailOwners(resourceContext, defined.Name, resourceContextInsertDoc, uniqueGroupEmailIdentities, "User owned group email "+defined.Name+" has assigned roles for the resource", depth+1)
			} else {
				if _, ok := uniqueIdentities[defined.Name]; !ok || depth == 1 {
					uniqueIdentities[defined.Name] = struct{}{}
					resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners = append(
						resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners, resourceContext.GetUserContextItem(defined.Name, common.POLICYBINDING_USER_TYPE,
							desc, "", resourceContextInsertDoc.Account, nil))
				}
			}
		}

		for _, derived := range appDoc.DerivedOwners {
			if strings.HasSuffix(derived.Name, APP_USER_SUFFIX) {
				if depth <= 5 && len(derived.IdentityId) > 0 {
					// Recursive call
					getApplicationOwnersForPolicy(resourceContext, derived.IdentityId, azureTenantID, resourceContextInsertDoc, uniqueIdentities, getIdentityChainDescription(desc, derived.IdentityId, depth), depth+1)
				}
			} else if IsNonHumanEmail(derived.Name, resourceContext) {
				uniqueGroupEmailIdentities := make(map[string]struct{})
				getNonHumanEmailOwners(resourceContext, derived.Name, resourceContextInsertDoc, uniqueGroupEmailIdentities, "User owned group email "+derived.Name+" has assigned roles for the resource", depth+1)
			} else {
				if _, ok := uniqueIdentities[derived.Name]; !ok || depth == 1 {
					uniqueIdentities[derived.Name] = struct{}{}
					resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners = append(
						resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners, resourceContext.GetUserContextItem(derived.Name, common.POLICYBINDING_USER_TYPE,
							desc, "", resourceContextInsertDoc.Account, nil))
				}
			}
		}
	}
}

func getAzureEventActor(eventJSONString string, resourceContext *ResourceContext, username *string) (actorType string) {

	var (
		azureLog AzureLog
	)

	if err := json.Unmarshal([]byte(eventJSONString), &azureLog); err != nil {
		return
	}

	switch azureLog.Claims.IDType {
	case "app":
		azureTenantID := azureLog.Claims.TenantID

		tenantContextID := common.GenerateCombinedHashID(azureTenantID, common.AZURE_TENANT_RESOURCE_TYPE, azureTenantID, resourceContext.LastCollectedAt, resourceContext.TenantID)

		var originString = "External "

		if _, ok := resourceContext.GetResourceContextInsertDoc(tenantContextID); ok {
			originString = "Internal "
		}

		*username += APP_USER_SUFFIX
		actorType = originString + "Azure Application"
	default:
		actorType = ""
	}

	return
}
