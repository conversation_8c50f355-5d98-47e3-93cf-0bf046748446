package context

import (
	"encoding/json"
	"sort"
	"strings"
	"sync"

	"github.com/precize/common"
	"github.com/precize/elastic"
	"github.com/precize/logger"
)

var parentOwnerPermissionSubstrings = []string{
	"folders.create", "folders.update", "folders.set", "projects.create", "projects.update", "projects.set", "organizations.create", "organizations.update", "organizations.set",
}

func GetRoles(resourceContext *ResourceContext) {

	logger.Print(logger.INFO, "Gathering started of roles", []string{resourceContext.TenantID})

	var (
		searchAfter        any
		roleResourcesQuery = `{"_source":["entityId","entityJson"],"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + resourceContext.TenantID + `"}},{"match":{"entityType.keyword":"` + common.GCP_ROLE_RESOURCE_TYPE + `"}},{"term":{"collectedAt":` + resourceContext.LastCollectedAt + `}},{"term":{"serviceId":` + common.GCP_SERVICE_ID + `}}]}}}`
	)

	for {
		roleResourcesDocs, sortResponse, err := elastic.ExecuteLargeSearchQuery([]string{elastic.CLOUD_RESOURCES_INDEX}, roleResourcesQuery, searchAfter)
		if err != nil {
			return
		}

		if len(roleResourcesDocs) > 0 {
			searchAfter = sortResponse
		} else {
			break
		}

		for _, roleResourcesDoc := range roleResourcesDocs {

			if roleID, ok := roleResourcesDoc["entityId"].(string); ok {

				if entityJson, ok := roleResourcesDoc["entityJson"].(string); ok {

					entityJsonMap := make(map[string]any)

					if err := json.Unmarshal([]byte(entityJson), &entityJsonMap); err != nil {
						logger.Print(logger.ERROR, "Failed to unmarshal", err)
						continue
					}

					if role, ok := entityJsonMap["role"].(map[string]any); ok {
						if permissions, ok := role["includedPermissions"].([]any); ok {
							for _, permission := range permissions {
								if permissionString, ok := permission.(string); ok && len(permissionString) > 0 {

									for _, permissionSubstring := range parentOwnerPermissionSubstrings {
										if strings.Contains(strings.ToLower(permissionString), permissionSubstring) {
											resourceContext.SetGCPParentOwnerRoles(strings.ToLower(roleID))
										}
									}

									if strings.ToLower(permissionString) == "iam.serviceaccountkeys.create" {
										resourceContext.SetGCPServiceAccountKeyCreatorRoles(strings.ToLower(roleID))
									}
								}
							}
						}
					}
				}
			}
		}
	}

	logger.Print(logger.INFO, "Gathering complete of roles", []string{resourceContext.TenantID})
}

func GetGCPGroupContext(resourceContext *ResourceContext) {
	logger.Print(logger.INFO, "Processing started for gcp group context", []string{resourceContext.TenantID})

	var (
		searchAfter         any
		groupResourcesQuery = `{"_source":["entityId","accountId","tags","region","entityJson"],"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + resourceContext.TenantID + `"}},{"match":{"entityType.keyword":"` + common.GCP_GROUP_RESOURCE_TYPE + `"}},{"term":{"collectedAt":` + resourceContext.LastCollectedAt + `}},{"term":{"serviceId":` + common.GCP_SERVICE_ID + `}}]}}}`
		groupResourcesChan  = make(chan map[string]map[string]any, MAX_PARENT_THREAD)
		wg                  sync.WaitGroup
		batchWg             sync.WaitGroup
		semaphore           = make(chan struct{}, MAX_PARENT_THREAD)
		collectedDocIDs     []string
		mutex               sync.Mutex
	)

	go func() {
		defer close(groupResourcesChan)
		for {
			groupResourcesDocs, sortResponse, err := elastic.ExecuteLargeSearchQuery([]string{elastic.CLOUD_RESOURCES_INDEX}, groupResourcesQuery, searchAfter)
			if err != nil {
				return
			}

			if len(groupResourcesDocs) > 0 {
				searchAfter = sortResponse
				groupResourcesChan <- groupResourcesDocs
			} else {
				return
			}
		}
	}()

	for groupResourcesDocs := range groupResourcesChan {
		for groupResourcesDocID, groupResourcesDoc := range groupResourcesDocs {
			semaphore <- struct{}{}
			wg.Add(1)

			go func(docID string, doc map[string]any) {
				defer wg.Done()
				defer func() { <-semaphore }()

				contextDocID := processGCPGroup(resourceContext, doc, docID)

				if contextDocID != "" {
					mutex.Lock()
					collectedDocIDs = append(collectedDocIDs, contextDocID)

					if len(collectedDocIDs) > 999 {
						docIDsBatch := make([]string, len(collectedDocIDs))
						copy(docIDsBatch, collectedDocIDs)

						batchWg.Add(1)
						go func(batch []string) {
							defer batchWg.Done()
							processActivityBatch(resourceContext, batch)
							processPreviousContext(resourceContext, batch)
						}(docIDsBatch)

						collectedDocIDs = []string{}
					}
					mutex.Unlock()
				}

			}(groupResourcesDocID, groupResourcesDoc)
		}
	}

	wg.Wait()
	batchWg.Wait()

	if len(collectedDocIDs) > 0 {
		processActivityBatch(resourceContext, collectedDocIDs)
		processPreviousContext(resourceContext, collectedDocIDs)
	}

	logger.Print(logger.INFO, "Processing complete for gcp group context", []string{resourceContext.TenantID})
}

func processGCPGroup(resourceContext *ResourceContext, groupResourcesDoc map[string]any, groupResourcesDocID string) (contextDocID string) {

	var (
		resourceContextInsertDoc common.ResourceContextInsertDoc
		groupName                string
	)

	if groupID, ok := groupResourcesDoc["entityId"].(string); ok {

		if projectID, ok := groupResourcesDoc["accountId"].(string); ok {

			if region, ok := groupResourcesDoc["region"].(string); ok {

				groupEntityJSON, ok := groupResourcesDoc["entityJson"].(string)
				if ok {
					groupName = getNameForGCPResource(groupID, common.GCP_GROUP_RESOURCE_TYPE, groupEntityJSON)
				}

				contextDocID = common.GenerateCombinedHashID(groupID, common.GCP_GROUP_RESOURCE_TYPE, projectID, resourceContext.LastCollectedAt, resourceContext.TenantID)

				resourceContextInsertDoc = common.ResourceContextInsertDoc{
					ResourceID:         groupID,
					ResourceName:       groupName,
					ResourceType:       common.GCP_GROUP_RESOURCE_TYPE,
					Account:            projectID,
					TenantID:           resourceContext.TenantID,
					Region:             region,
					ServiceID:          common.GCP_SERVICE_ID_INT,
					CloudResourceDocID: groupResourcesDocID,
				}

				setCustomerEntityIncludeContextOfResource(resourceContext, &resourceContextInsertDoc)

				if envName := GetEnvironmentNameFromValue(groupName); len(envName) > 0 {
					resourceContextInsertDoc.ResourceEnvTypes.DerivedEnv = append(resourceContextInsertDoc.ResourceEnvTypes.DerivedEnv,
						common.ResourceContextItem{
							Name: envName,
							Type: common.RESOURCE_NAME_ENV_TYPE,
						},
					)
				}

				projectContextID := common.GenerateCombinedHashID(projectID, common.GCP_PROJECT_RESOURCE_TYPE, projectID, resourceContext.LastCollectedAt, resourceContext.TenantID)

				if projectRscCtxInsertDoc, ok := resourceContext.GetResourceContextInsertDoc(projectContextID); ok {
					resourceContextInsertDoc.ResourceOwnerTypes.InheritedOwners = getInheritedOwners(projectRscCtxInsertDoc, common.GCP_PROJECT_RESOURCE_TYPE)
					resourceContextInsertDoc.ResourceEnvTypes.InheritedEnv = getInheritedEnv(projectRscCtxInsertDoc)
				}

				resourceContext.SetResourceContextInsertDoc(contextDocID, resourceContextInsertDoc)
			}
		}
	}

	return
}

func GetGCPOrgContext(resourceContext *ResourceContext) {
	logger.Print(logger.INFO, "Processing started for gcp organization context", []string{resourceContext.TenantID})

	var (
		searchAfter       any
		orgResourcesQuery = `{"_source":["entityId","tags","entityJson"],"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + resourceContext.TenantID + `"}},{"match":{"entityType.keyword":"` + common.GCP_ORG_RESOURCE_TYPE + `"}},{"term":{"collectedAt":` + resourceContext.LastCollectedAt + `}},{"term":{"serviceId":` + common.GCP_SERVICE_ID + `}}]}}}`
		orgResourcesChan  = make(chan map[string]map[string]any, MAX_PARENT_THREAD)
		wg                sync.WaitGroup
		semaphore         = make(chan struct{}, MAX_PARENT_THREAD)
	)

	go func() {
		defer close(orgResourcesChan)
		for {
			orgResourcesDocs, sortResponse, err := elastic.ExecuteLargeSearchQuery([]string{elastic.CLOUD_RESOURCES_INDEX}, orgResourcesQuery, searchAfter)
			if err != nil {
				return
			}

			if len(orgResourcesDocs) > 0 {
				searchAfter = sortResponse
				orgResourcesChan <- orgResourcesDocs
			} else {
				return
			}
		}
	}()

	for orgResourcesDocs := range orgResourcesChan {
		for orgResourcesDocID, orgResourcesDoc := range orgResourcesDocs {
			semaphore <- struct{}{}
			wg.Add(1)

			go func(docID string, doc map[string]any) {
				defer wg.Done()
				defer func() { <-semaphore }()
				processGCPOrg(resourceContext, docID, doc)
			}(orgResourcesDocID, orgResourcesDoc)
		}
	}

	wg.Wait()
	logger.Print(logger.INFO, "Processing complete for gcp organization context", []string{resourceContext.TenantID})
}

func processGCPOrg(resourceContext *ResourceContext, orgResourcesDocID string, orgResourcesDoc map[string]any) {
	var (
		resourceContextInsertDoc common.ResourceContextInsertDoc
		orgName                  string
		ownerGroups              []string
	)

	if orgID, ok := orgResourcesDoc["entityId"].(string); ok {

		orgOwners := []string{}
		contextDocID := common.GenerateCombinedHashID(orgID, common.GCP_ORG_RESOURCE_TYPE, orgID, resourceContext.LastCollectedAt, resourceContext.TenantID)

		resourceContextInsertDoc = common.ResourceContextInsertDoc{
			ResourceID:         orgID,
			ResourceName:       orgName,
			ResourceType:       common.GCP_ORG_RESOURCE_TYPE,
			Account:            orgID,
			TenantID:           resourceContext.TenantID,
			Region:             "Global",
			ServiceID:          common.GCP_SERVICE_ID_INT,
			CloudResourceDocID: orgResourcesDocID,
		}

		setCustomerEntityIncludeContextOfResource(resourceContext, &resourceContextInsertDoc)
		getTagContextOfResource(resourceContext, orgResourcesDoc, &resourceContextInsertDoc)

		var (
			userSearchAfter    any
			userResourcesQuery = `{"_source":["entityId","entityType","entityJson"],"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + resourceContext.TenantID + `"}},{"terms":{"entityType.keyword":["` + common.GCP_IAM_RESOURCE_TYPE + `","` + common.GCP_GROUP_RESOURCE_TYPE + `"]}},{"term":{"collectedAt":` + resourceContext.LastCollectedAt + `}},{"match":{"accountId.keyword":"` + orgID + `"}},{"term":{"serviceId":` + common.GCP_SERVICE_ID + `}}]}}}`
		)

		for {
			userResourcesDocs, userSortResponse, err := elastic.ExecuteLargeSearchQuery([]string{elastic.CLOUD_RESOURCES_INDEX}, userResourcesQuery, userSearchAfter)
			if err != nil {
				return
			}

			if len(userResourcesDocs) > 0 {
				userSearchAfter = userSortResponse
			} else {
				break
			}

			for _, userResourcesDoc := range userResourcesDocs {

				if username, ok := userResourcesDoc["entityId"].(string); ok {

					if userEntityJSON, ok := userResourcesDoc["entityJson"].(string); ok {

						userEntityJSONMap := make(map[string]any)
						if err := json.Unmarshal([]byte(userEntityJSON), &userEntityJSONMap); err != nil {
							logger.Print(logger.ERROR, "Failed to unmarshal", err)
							return
						}

						if rolesInterface, ok := userEntityJSONMap["roles"].([]any); ok {

							for _, roleInterface := range rolesInterface {

								if role, ok := roleInterface.(string); ok {
									if resourceContext.GetGCPParentOwnerRoles(strings.ToLower(role)) {

										if entityType, _ := userResourcesDoc["entityType"]; entityType == common.GCP_GROUP_RESOURCE_TYPE {
											ownerGroups = append(ownerGroups, username)
											continue
										}

										resourceContextInsertDoc.ResourceOwnerTypes.DefinedOwners = append(
											resourceContextInsertDoc.ResourceOwnerTypes.DefinedOwners,
											resourceContext.GetUserContextItem(username, common.ORG_OWNER_USER_TYPE, "", "", resourceContextInsertDoc.Account, nil),
										)

										orgOwners = append(orgOwners, username)
									}
								}
							}
						}
					}
				}
			}
		}

		for _, ownerGroup := range ownerGroups {

			groupDocID := common.GenerateCombinedHashID(ownerGroup, common.GCP_GROUP_RESOURCE_TYPE, orgID, resourceContext.LastCollectedAt, resourceContext.TenantID)
			if groupDoc, ok := resourceContext.GetResourceContextInsertDoc(groupDocID); ok {
				// Groups are from google workspace. Only activity owners will be there. Consider top 2
				for i, groupDerivedOwner := range groupDoc.ResourceOwnerTypes.DerivedOwners {
					if i <= 2 {
						resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners = append(resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners,
							resourceContext.GetUserContextItem(groupDerivedOwner.Name, common.PROJECT_GROUPOWNER_USER_TYPE, "User is the owner of "+ownerGroup+" group which has owner or equivalent role in the org", "", resourceContextInsertDoc.Account, nil),
						)

						orgOwners = append(orgOwners, groupDerivedOwner.Name)
					}
				}
			}
		}

		if entityJSON, ok := orgResourcesDoc["entityJson"].(string); ok {

			orgName = getNameForGCPResource(orgID, common.GCP_ORG_RESOURCE_TYPE, entityJSON)
			if envName := GetEnvironmentNameFromValue(orgName); len(envName) > 0 {
				resourceContextInsertDoc.ResourceEnvTypes.DerivedEnv = append(resourceContextInsertDoc.ResourceEnvTypes.DerivedEnv,
					common.ResourceContextItem{
						Name: envName,
						Type: common.ORG_NAME_ENV_TYPE,
					},
				)
			}

			if len(orgName) > 0 {
				resourceContextInsertDoc.ResourceName = orgName
			}
		}

		// TODO: Get org cost, sec and ops owners

		resourceContext.SetGCPParentOwners(orgID, orgOwners)
		resourceContext.SetResourceContextInsertDoc(contextDocID, resourceContextInsertDoc)

	}
}

func GetFolderContext(resourceContext *ResourceContext) {
	logger.Print(logger.INFO, "Processing started for gcp folder context", []string{resourceContext.TenantID})

	var (
		searchAfter          any
		folderResourcesQuery = `{"_source":["entityId","tags","accountId","entityJson"],"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + resourceContext.TenantID + `"}},{"term":{"entityType.keyword":"` + common.GCP_FOLDER_RESOURCE_TYPE + `"}},{"term":{"collectedAt":` + resourceContext.LastCollectedAt + `}},{"term":{"serviceId":` + common.GCP_SERVICE_ID + `}}]}}}`
		folderResourcesChan  = make(chan map[string]map[string]any, MAX_PARENT_THREAD)
		wg                   sync.WaitGroup
		batchWg              sync.WaitGroup
		semaphore            = make(chan struct{}, MAX_PARENT_THREAD)
		collectedDocIDs      []string
		mutex                sync.Mutex
	)

	go func() {
		defer close(folderResourcesChan)
		for {
			folderResourcesDocs, sortResponse, err := elastic.ExecuteLargeSearchQuery([]string{elastic.CLOUD_RESOURCES_INDEX}, folderResourcesQuery, searchAfter)
			if err != nil {
				return
			}

			if len(folderResourcesDocs) > 0 {
				searchAfter = sortResponse
				folderResourcesChan <- folderResourcesDocs
			} else {
				return
			}
		}
	}()

	for folderResourcesDocs := range folderResourcesChan {
		for folderResourcesDocID, folderResourcesDoc := range folderResourcesDocs {
			semaphore <- struct{}{}
			wg.Add(1)

			go func(docID string, doc map[string]any) {
				defer wg.Done()
				defer func() { <-semaphore }()

				contextDocID := processGCPFolder(resourceContext, doc, docID)

				if contextDocID != "" {
					mutex.Lock()
					collectedDocIDs = append(collectedDocIDs, contextDocID)

					if len(collectedDocIDs) > 999 {
						docIDsBatch := make([]string, len(collectedDocIDs))
						copy(docIDsBatch, collectedDocIDs)

						batchWg.Add(1)
						go func(batch []string) {
							defer batchWg.Done()
							processActivityBatch(resourceContext, batch)
							processPreviousContext(resourceContext, batch)
							processResourceNamesForTeams(resourceContext, batch)
							processResourceNamesForApps(resourceContext, batch)
						}(docIDsBatch)

						collectedDocIDs = []string{}
					}
					mutex.Unlock()
				}

			}(folderResourcesDocID, folderResourcesDoc)
		}
	}

	wg.Wait()
	batchWg.Wait()

	if len(collectedDocIDs) > 0 {
		processActivityBatch(resourceContext, collectedDocIDs)
		processPreviousContext(resourceContext, collectedDocIDs)
		processResourceNamesForTeams(resourceContext, collectedDocIDs)
		processResourceNamesForApps(resourceContext, collectedDocIDs)
	}

	// Process folder hierarchies
	searchAfter = nil
	folderResourcesChan = make(chan map[string]map[string]any, MAX_PARENT_THREAD)
	semaphore = make(chan struct{}, MAX_PARENT_THREAD)

	go func() {
		for {
			folderResourcesDocs, sortResponse, err := elastic.ExecuteLargeSearchQuery([]string{elastic.CLOUD_RESOURCES_INDEX}, folderResourcesQuery, searchAfter)
			if err != nil {
				close(folderResourcesChan)
				return
			}

			if len(folderResourcesDocs) > 0 {
				searchAfter = sortResponse
				folderResourcesChan <- folderResourcesDocs
			} else {
				close(folderResourcesChan)
				return
			}
		}
	}()

	for folderResourcesDocs := range folderResourcesChan {
		for _, folderResourcesDoc := range folderResourcesDocs {
			semaphore <- struct{}{}

			wg.Add(1)
			go func(doc map[string]any) {
				defer wg.Done()
				defer func() { <-semaphore }()

				processFolderHierarchies(resourceContext, doc)
			}(folderResourcesDoc)
		}
	}

	wg.Wait()

	logger.Print(logger.INFO, "Processing complete for gcp folder context", []string{resourceContext.TenantID})
}

func processGCPFolder(resourceContext *ResourceContext, folderResourcesDoc map[string]any, folderResourcesDocID string) (contextDocID string) {

	var (
		resourceContextInsertDoc common.ResourceContextInsertDoc
		folderName               string
		ownerGroups              []string
	)

	if folderID, ok := folderResourcesDoc["entityId"].(string); ok {

		folderOwners := []string{}

		orgOrFolderID, _ := folderResourcesDoc["accountId"].(string)

		entityJSONString, _ := folderResourcesDoc["entityJson"].(string)

		folderName = getNameForGCPResource(folderID, common.GCP_FOLDER_RESOURCE_TYPE, entityJSONString)

		contextDocID = common.GenerateCombinedHashID(folderID, common.GCP_FOLDER_RESOURCE_TYPE, folderID, resourceContext.LastCollectedAt, resourceContext.TenantID)

		resourceContextInsertDoc = common.ResourceContextInsertDoc{
			ResourceID:         folderID,
			ResourceName:       folderName,
			ResourceType:       common.GCP_FOLDER_RESOURCE_TYPE,
			Account:            orgOrFolderID,
			TenantID:           resourceContext.TenantID,
			Region:             "Global",
			ServiceID:          common.GCP_SERVICE_ID_INT,
			CloudResourceDocID: folderResourcesDocID,
		}

		setCustomerEntityIncludeContextOfResource(resourceContext, &resourceContextInsertDoc)
		getTagContextOfResource(resourceContext, folderResourcesDoc, &resourceContextInsertDoc)

		entityJSON := make(map[string]any)
		if err := json.Unmarshal([]byte(entityJSONString), &entityJSON); err != nil {
			logger.Print(logger.ERROR, "Failed to unmarshal", err)
			return
		}

		getTTLFromResourceName(resourceContext, &resourceContextInsertDoc, entityJSON)

		var (
			userSearchAfter    any
			userResourcesQuery = `{"_source":["entityId","entityType","entityJson"],"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + resourceContext.TenantID + `"}},{"terms":{"entityType.keyword":["` + common.GCP_IAM_RESOURCE_TYPE + `","` + common.GCP_GROUP_RESOURCE_TYPE + `"]}},{"term":{"collectedAt":` + resourceContext.LastCollectedAt + `}},{"match":{"accountId.keyword":"` + folderID + `"}},{"term":{"serviceId":` + common.GCP_SERVICE_ID + `}}]}}}`
		)

		for {
			userResourcesDocs, userSortResponse, err := elastic.ExecuteLargeSearchQuery([]string{elastic.CLOUD_RESOURCES_INDEX}, userResourcesQuery, userSearchAfter)
			if err != nil {
				return
			}

			if len(userResourcesDocs) > 0 {
				userSearchAfter = userSortResponse
			} else {
				break
			}

			for _, userResourcesDoc := range userResourcesDocs {

				if username, ok := userResourcesDoc["entityId"].(string); ok {

					if userEntityJSON, ok := userResourcesDoc["entityJson"].(string); ok {

						userEntityJSONMap := make(map[string]any)
						if err := json.Unmarshal([]byte(userEntityJSON), &userEntityJSONMap); err != nil {
							logger.Print(logger.ERROR, "Failed to unmarshal", err)
							return
						}

						if rolesInterface, ok := userEntityJSONMap["roles"].([]any); ok {

							for _, roleInterface := range rolesInterface {

								if role, ok := roleInterface.(string); ok {
									if resourceContext.GetGCPParentOwnerRoles(strings.ToLower(role)) {

										if entityType, _ := userResourcesDoc["entityType"]; entityType == common.GCP_GROUP_RESOURCE_TYPE {
											ownerGroups = append(ownerGroups, username)
											continue
										}

										resourceContextInsertDoc.ResourceOwnerTypes.DefinedOwners = append(
											resourceContextInsertDoc.ResourceOwnerTypes.DefinedOwners,
											resourceContext.GetUserContextItem(username, common.FOLDER_OWNER_USER_TYPE, "", "", resourceContextInsertDoc.Account, nil),
										)

										folderOwners = append(folderOwners, username)
									}
								}
							}
						}
					}
				}
			}
		}

		for _, ownerGroup := range ownerGroups {

			groupDocID := common.GenerateCombinedHashID(ownerGroup, common.GCP_GROUP_RESOURCE_TYPE, folderID, resourceContext.LastCollectedAt, resourceContext.TenantID)
			if groupDoc, ok := resourceContext.GetResourceContextInsertDoc(groupDocID); ok {
				// Groups are from google workspace. Only activity owners will be there. Consider top 2
				for i, groupDerivedOwner := range groupDoc.ResourceOwnerTypes.DerivedOwners {
					if i <= 2 {
						resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners = append(resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners,
							resourceContext.GetUserContextItem(groupDerivedOwner.Name, common.PROJECT_GROUPOWNER_USER_TYPE, "User is the owner of "+ownerGroup+" group which has owner or equivalent role in the folder", "", resourceContextInsertDoc.Account, nil),
						)

						folderOwners = append(folderOwners, groupDerivedOwner.Name)
					}
				}
			}
		}

		resourceContext.SetGCPParentOwners(folderID, folderOwners)

		if envName := GetEnvironmentNameFromValue(folderName); len(envName) > 0 {
			resourceContextInsertDoc.ResourceEnvTypes.DerivedEnv = append(resourceContextInsertDoc.ResourceEnvTypes.DerivedEnv,
				common.ResourceContextItem{
					Name: envName,
					Type: common.FOLDER_NAME_ENV_TYPE,
				},
			)

			incrementParentChildEnvCount(resourceContext, envName, orgOrFolderID, "")
		}

		if team := GetTeamNameFromValue(folderName); len(team) > 0 {
			resourceContextInsertDoc.ResourceTeamTypes.DerivedTeam = append(resourceContextInsertDoc.ResourceTeamTypes.DerivedTeam,
				common.ResourceContextItem{
					Name: team,
					Type: common.FOLDER_NAME_TEAM_TYPE,
				},
			)
		}

		resourceContext.SetResourceContextInsertDoc(contextDocID, resourceContextInsertDoc)
	}

	return
}

func processFolderHierarchies(resourceContext *ResourceContext, folderResourcesDoc map[string]any) {

	if folderID, ok := folderResourcesDoc["entityId"].(string); ok {

		contextDocID := common.GenerateCombinedHashID(folderID, common.GCP_FOLDER_RESOURCE_TYPE, folderID, resourceContext.LastCollectedAt, resourceContext.TenantID)

		if resourceContextInsertDoc, ok := resourceContext.GetResourceContextInsertDoc(contextDocID); ok {
			getHierarchicalContext(contextDocID, resourceContext, &resourceContextInsertDoc)
			resourceContext.SetResourceContextInsertDoc(contextDocID, resourceContextInsertDoc)
		}
	}
}

func GetProjectContext(resourceContext *ResourceContext) {
	logger.Print(logger.INFO, "Processing started for gcp project context", []string{resourceContext.TenantID})

	var (
		searchAfter           any
		projectResourcesQuery = `{"_source":["entityId","tags","accountId","entityJson"],"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + resourceContext.TenantID + `"}},{"match":{"entityType.keyword":"` + common.GCP_PROJECT_RESOURCE_TYPE + `"}},{"term":{"collectedAt":` + resourceContext.LastCollectedAt + `}},{"term":{"serviceId":` + common.GCP_SERVICE_ID + `}}]}}}`
		projectResourcesChan  = make(chan map[string]map[string]any, MAX_PARENT_THREAD)
		wg                    sync.WaitGroup
		batchWg               sync.WaitGroup
		semaphore             = make(chan struct{}, MAX_PARENT_THREAD)
		collectedDocIDs       []string
		mutex                 sync.Mutex
	)

	go func() {
		defer close(projectResourcesChan)
		for {
			projectResourcesDocs, sortResponse, err := elastic.ExecuteLargeSearchQuery([]string{elastic.CLOUD_RESOURCES_INDEX}, projectResourcesQuery, searchAfter)
			if err != nil {
				return
			}

			if len(projectResourcesDocs) > 0 {
				searchAfter = sortResponse
				projectResourcesChan <- projectResourcesDocs
			} else {
				return
			}
		}
	}()

	for projectResourcesDocs := range projectResourcesChan {
		for projectResourcesDocID, projectResourcesDoc := range projectResourcesDocs {
			semaphore <- struct{}{}
			wg.Add(1)

			go func(docID string, doc map[string]any) {
				defer wg.Done()
				defer func() { <-semaphore }()

				contextDocID := processProject(resourceContext, doc, docID)

				if contextDocID != "" {
					mutex.Lock()
					collectedDocIDs = append(collectedDocIDs, contextDocID)

					if len(collectedDocIDs) > 999 {
						docIDsBatch := make([]string, len(collectedDocIDs))
						copy(docIDsBatch, collectedDocIDs)

						batchWg.Add(1)
						go func(batch []string) {
							defer batchWg.Done()
							processActivityBatch(resourceContext, batch)
							processPreviousContext(resourceContext, batch)
							processResourceNamesForTeams(resourceContext, batch)
							processResourceNamesForApps(resourceContext, batch)
						}(docIDsBatch)

						collectedDocIDs = []string{}
					}
					mutex.Unlock()
				}
			}(projectResourcesDocID, projectResourcesDoc)
		}
	}

	wg.Wait()
	batchWg.Wait()

	if len(collectedDocIDs) > 0 {
		processActivityBatch(resourceContext, collectedDocIDs)
		processPreviousContext(resourceContext, collectedDocIDs)
		processResourceNamesForTeams(resourceContext, collectedDocIDs)
		processResourceNamesForApps(resourceContext, collectedDocIDs)
	}

	logger.Print(logger.INFO, "Processing complete for gcp project context", []string{resourceContext.TenantID})
}

func processProject(resourceContext *ResourceContext, projectResourcesDoc map[string]any, projectResourcesDocID string) (contextDocID string) {

	var (
		resourceContextInsertDoc common.ResourceContextInsertDoc
		projectName              string
		ownerGroups              []string
	)

	if projectID, ok := projectResourcesDoc["entityId"].(string); ok {

		orgOrFolderID, _ := projectResourcesDoc["accountId"].(string)

		entityJSONString, _ := projectResourcesDoc["entityJson"].(string)

		projectName = getNameForGCPResource(projectID, common.GCP_PROJECT_RESOURCE_TYPE, entityJSONString)

		contextDocID = common.GenerateCombinedHashID(projectID, common.GCP_PROJECT_RESOURCE_TYPE, projectID, resourceContext.LastCollectedAt, resourceContext.TenantID)

		resourceContextInsertDoc = common.ResourceContextInsertDoc{
			ResourceID:         projectID,
			ResourceName:       projectName,
			ResourceType:       common.GCP_PROJECT_RESOURCE_TYPE,
			Account:            orgOrFolderID,
			TenantID:           resourceContext.TenantID,
			Region:             "Global",
			ServiceID:          common.GCP_SERVICE_ID_INT,
			CloudResourceDocID: projectResourcesDocID,
		}

		resourceContext.SetGCPProjectNumberToID(projectID, projectName)
		resourceContext.SetGCPProjectIDToNumber(projectName, projectID)

		setCustomerEntityIncludeContextOfResource(resourceContext, &resourceContextInsertDoc)
		getTagContextOfResource(resourceContext, projectResourcesDoc, &resourceContextInsertDoc)

		entityJSON := make(map[string]any)
		if err := json.Unmarshal([]byte(entityJSONString), &entityJSON); err != nil {
			logger.Print(logger.ERROR, "Failed to unmarshal", err)
			return
		}

		getTTLFromResourceName(resourceContext, &resourceContextInsertDoc, entityJSON)

		var (
			userSearchAfter    any
			userResourcesQuery = `{"_source":["entityId","entityType","entityJson"],"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + resourceContext.TenantID + `"}},{"terms":{"entityType.keyword":["` + common.GCP_IAM_RESOURCE_TYPE + `","` + common.GCP_GROUP_RESOURCE_TYPE + `"]}},{"term":{"collectedAt":` + resourceContext.LastCollectedAt + `}},{"match":{"accountId.keyword":"` + projectID + `"}},{"term":{"serviceId":` + common.GCP_SERVICE_ID + `}}]}}}`
		)

		for {
			userResourcesDocs, userSortResponse, err := elastic.ExecuteLargeSearchQuery([]string{elastic.CLOUD_RESOURCES_INDEX}, userResourcesQuery, userSearchAfter)
			if err != nil {
				return
			}

			if len(userResourcesDocs) > 0 {
				userSearchAfter = userSortResponse
			} else {
				break
			}

			for _, userResourcesDoc := range userResourcesDocs {

				if username, ok := userResourcesDoc["entityId"].(string); ok {

					if userEntityJSON, ok := userResourcesDoc["entityJson"].(string); ok {

						userEntityJSONMap := make(map[string]any)
						if err := json.Unmarshal([]byte(userEntityJSON), &userEntityJSONMap); err != nil {
							logger.Print(logger.ERROR, "Failed to unmarshal", err)
							return
						}

						if rolesInterface, ok := userEntityJSONMap["roles"].([]any); ok {

							for _, roleInterface := range rolesInterface {

								if role, ok := roleInterface.(string); ok {
									if resourceContext.GetGCPParentOwnerRoles(strings.ToLower(role)) {

										if entityType, _ := userResourcesDoc["entityType"]; entityType == common.GCP_GROUP_RESOURCE_TYPE {
											ownerGroups = append(ownerGroups, username)
											continue
										}

										resourceContextInsertDoc.ResourceOwnerTypes.DefinedOwners = append(resourceContextInsertDoc.ResourceOwnerTypes.DefinedOwners,
											resourceContext.GetUserContextItem(username, common.PROJECT_OWNER_USER_TYPE, "", "", resourceContextInsertDoc.Account, nil),
										)
									}
								}
							}
						}
					}
				}
			}
		}

		for _, ownerGroup := range ownerGroups {

			groupDocID := common.GenerateCombinedHashID(ownerGroup, common.GCP_GROUP_RESOURCE_TYPE, projectID, resourceContext.LastCollectedAt, resourceContext.TenantID)
			if groupDoc, ok := resourceContext.GetResourceContextInsertDoc(groupDocID); ok {
				// Groups are from google workspace. Only activity owners will be there. Consider top 2
				for i, groupDerivedOwner := range groupDoc.ResourceOwnerTypes.DerivedOwners {
					if i <= 2 {
						resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners = append(resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners,
							resourceContext.GetUserContextItem(groupDerivedOwner.Name, common.PROJECT_GROUPOWNER_USER_TYPE, "User is the owner of "+ownerGroup+" group which has owner or equivalent role in the project", "", resourceContextInsertDoc.Account, nil),
						)
					}
				}
			}
		}

		if envName := GetEnvironmentNameFromValue(projectName); len(envName) > 0 {
			resourceContextInsertDoc.ResourceEnvTypes.DerivedEnv = append(resourceContextInsertDoc.ResourceEnvTypes.DerivedEnv,
				common.ResourceContextItem{
					Name: envName,
					Type: common.PROJECT_NAME_ENV_TYPE,
				},
			)

			incrementParentChildEnvCount(resourceContext, envName, orgOrFolderID, "")
		}

		if team := GetTeamNameFromValue(projectName); len(team) > 0 {
			resourceContextInsertDoc.ResourceTeamTypes.DerivedTeam = append(resourceContextInsertDoc.ResourceTeamTypes.DerivedTeam,
				common.ResourceContextItem{
					Name: team,
					Type: common.PROJECT_NAME_TEAM_TYPE,
				},
			)
		}

		getHierarchicalContext(contextDocID, resourceContext, &resourceContextInsertDoc)

		resourceContext.SetResourceContextInsertDoc(contextDocID, resourceContextInsertDoc)
	}

	return
}

func GetServiceAccountKeyContext(resourceContext *ResourceContext) {

	logger.Print(logger.INFO, "Processing started for gcp service account key context", []string{resourceContext.TenantID})

	var (
		searchAfter                     any
		serviceAccountKeyResourcesQuery = `{"_source":["entityId","accountId","tags","region","entityJson"],"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + resourceContext.TenantID + `"}},{"match":{"entityType.keyword":"` + common.GCP_SERVICEACCOUNTKEY_RESOURCE_TYPE + `"}},{"term":{"collectedAt":` + resourceContext.LastCollectedAt + `}},{"term":{"serviceId":` + common.GCP_SERVICE_ID + `}}]}}}`
	)

	for {
		serviceAccountKeyResourcesDocs, sortResponse, err := elastic.ExecuteLargeSearchQuery([]string{elastic.CLOUD_RESOURCES_INDEX}, serviceAccountKeyResourcesQuery, searchAfter)
		if err != nil {
			return
		}

		if len(serviceAccountKeyResourcesDocs) > 0 {
			searchAfter = sortResponse
		} else {
			break
		}

		var (
			countForCollectedActivityFetch int
			collectedDocIDs                []string
		)

		for serviceAccountKeyResourcesDocID, serviceAccountKeyResourcesDoc := range serviceAccountKeyResourcesDocs {

			var (
				resourceContextInsertDoc common.ResourceContextInsertDoc
				serviceAccountKeyName    string
			)

			if serviceAccountKeyID, ok := serviceAccountKeyResourcesDoc["entityId"].(string); ok {

				if projectID, ok := serviceAccountKeyResourcesDoc["accountId"].(string); ok {

					if region, ok := serviceAccountKeyResourcesDoc["region"].(string); ok {

						if entityJSON, ok := serviceAccountKeyResourcesDoc["entityJson"].(string); ok {
							serviceAccountKeyName = getNameForGCPResource(serviceAccountKeyID, common.GCP_SERVICEACCOUNTKEY_RESOURCE_TYPE, entityJSON)
						}

						contextDocID := common.GenerateCombinedHashID(serviceAccountKeyID, common.GCP_SERVICEACCOUNTKEY_RESOURCE_TYPE, projectID, resourceContext.LastCollectedAt, resourceContext.TenantID)

						resourceContextInsertDoc = common.ResourceContextInsertDoc{
							ResourceID:         serviceAccountKeyID,
							ResourceName:       serviceAccountKeyName,
							ResourceType:       common.GCP_SERVICEACCOUNTKEY_RESOURCE_TYPE,
							Account:            projectID,
							TenantID:           resourceContext.TenantID,
							Region:             region,
							ServiceID:          common.GCP_SERVICE_ID_INT,
							CloudResourceDocID: serviceAccountKeyResourcesDocID,
						}

						setCustomerEntityIncludeContextOfResource(resourceContext, &resourceContextInsertDoc)

						projectContextID := common.GenerateCombinedHashID(projectID, common.GCP_PROJECT_RESOURCE_TYPE, projectID, resourceContext.LastCollectedAt, resourceContext.TenantID)

						if projRscCtxInsertDoc, ok := resourceContext.GetResourceContextInsertDoc(projectContextID); ok {
							resourceContextInsertDoc.ResourceOwnerTypes.InheritedOwners = getInheritedOwners(projRscCtxInsertDoc, common.GCP_PROJECT_RESOURCE_TYPE)
							resourceContextInsertDoc.ResourceEnvTypes.InheritedEnv = getInheritedEnv(projRscCtxInsertDoc)
						}

						resourceContext.SetResourceContextInsertDoc(contextDocID, resourceContextInsertDoc)

						countForCollectedActivityFetch++
						collectedDocIDs = append(collectedDocIDs, contextDocID)

						if countForCollectedActivityFetch > 999 {
							getActivityContextOfResource(resourceContext, collectedDocIDs)
							getCodeContextOfResource(resourceContext, collectedDocIDs)
							getJiraContextOfResource(resourceContext, collectedDocIDs)

							countForCollectedActivityFetch = 0
							collectedDocIDs = []string{}
						}
					}
				}
			}
		}

		if countForCollectedActivityFetch > 0 {
			if err = getActivityContextOfResource(resourceContext, collectedDocIDs); err != nil {
				break
			}

			if err = getCodeContextOfResource(resourceContext, collectedDocIDs); err != nil {
				break
			}

			if err = getJiraContextOfResource(resourceContext, collectedDocIDs); err != nil {
				break
			}

			countForCollectedActivityFetch = 0
		}
	}

	logger.Print(logger.INFO, "Processing complete for gcp service account key context", []string{resourceContext.TenantID})
}

func GetServiceAccountContext(resourceContext *ResourceContext) {
	logger.Print(logger.INFO, "Processing started for gcp service account context", []string{resourceContext.TenantID})

	var (
		searchAfter                  any
		serviceAccountResourcesQuery = `{"_source":["entityId","accountId","tags","region","entityJson"],"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + resourceContext.TenantID + `"}},{"match":{"entityType.keyword":"` + common.GCP_SERVICEACCOUNT_RESOURCE_TYPE + `"}},{"term":{"collectedAt":` + resourceContext.LastCollectedAt + `}},{"term":{"serviceId":` + common.GCP_SERVICE_ID + `}}]}}}`
		serviceAccountResourcesChan  = make(chan map[string]map[string]any, MAX_PARENT_THREAD)
		wg                           sync.WaitGroup
		batchWg                      sync.WaitGroup
		semaphore                    = make(chan struct{}, MAX_PARENT_THREAD)
		collectedDocIDs              []string
		mutex                        sync.Mutex
	)

	go func() {
		defer close(serviceAccountResourcesChan)
		for {
			serviceAccountResourcesDocs, sortResponse, err := elastic.ExecuteLargeSearchQuery([]string{elastic.CLOUD_RESOURCES_INDEX}, serviceAccountResourcesQuery, searchAfter)
			if err != nil {
				return
			}

			if len(serviceAccountResourcesDocs) > 0 {
				searchAfter = sortResponse
				serviceAccountResourcesChan <- serviceAccountResourcesDocs
			} else {
				return
			}
		}
	}()

	for serviceAccountResourcesDocs := range serviceAccountResourcesChan {
		for serviceAccountResourcesDocID, serviceAccountResourcesDoc := range serviceAccountResourcesDocs {
			semaphore <- struct{}{}
			wg.Add(1)

			go func(docID string, doc map[string]any) {
				defer wg.Done()
				defer func() { <-semaphore }()

				contextDocID := processServiceAccount(resourceContext, doc, docID)

				if contextDocID != "" {
					mutex.Lock()
					collectedDocIDs = append(collectedDocIDs, contextDocID)

					if len(collectedDocIDs) > 999 {
						docIDsBatch := make([]string, len(collectedDocIDs))
						copy(docIDsBatch, collectedDocIDs)

						batchWg.Add(1)
						go func(batch []string) {
							defer batchWg.Done()
							processActivityBatch(resourceContext, batch)
							processPreviousContext(resourceContext, batch)
							processResourceNamesForApps(resourceContext, batch)
						}(docIDsBatch)

						collectedDocIDs = []string{}
					}
					mutex.Unlock()
				}

			}(serviceAccountResourcesDocID, serviceAccountResourcesDoc)
		}
	}

	wg.Wait()
	batchWg.Wait()

	if len(collectedDocIDs) > 0 {
		processActivityBatch(resourceContext, collectedDocIDs)
		processPreviousContext(resourceContext, collectedDocIDs)
		processResourceNamesForApps(resourceContext, collectedDocIDs)
	}

	logger.Print(logger.INFO, "Processing complete for gcp service account context", []string{resourceContext.TenantID})
}

func processServiceAccount(resourceContext *ResourceContext, serviceAccountResourcesDoc map[string]any, serviceAccountResourcesDocID string) (contextDocID string) {

	var (
		resourceContextInsertDoc common.ResourceContextInsertDoc
		serviceAccountName       string
	)

	if serviceAccountID, ok := serviceAccountResourcesDoc["entityId"].(string); ok {

		if projectID, ok := serviceAccountResourcesDoc["accountId"].(string); ok {

			if region, ok := serviceAccountResourcesDoc["region"].(string); ok {

				serviceAccountEntityJSON, ok := serviceAccountResourcesDoc["entityJson"].(string)
				if ok {
					serviceAccountName = getNameForGCPResource(serviceAccountID, common.GCP_SERVICEACCOUNT_RESOURCE_TYPE, serviceAccountEntityJSON)
				}

				contextDocID = common.GenerateCombinedHashID(serviceAccountID, common.GCP_SERVICEACCOUNT_RESOURCE_TYPE, projectID, resourceContext.LastCollectedAt, resourceContext.TenantID)

				resourceContextInsertDoc = common.ResourceContextInsertDoc{
					ResourceID:         serviceAccountID,
					ResourceName:       serviceAccountName,
					ResourceType:       common.GCP_SERVICEACCOUNT_RESOURCE_TYPE,
					Account:            projectID,
					TenantID:           resourceContext.TenantID,
					Region:             region,
					ServiceID:          common.GCP_SERVICE_ID_INT,
					CloudResourceDocID: serviceAccountResourcesDocID,
				}

				setCustomerEntityIncludeContextOfResource(resourceContext, &resourceContextInsertDoc)
				getServiceAccountIAMPolicies(resourceContext, &resourceContextInsertDoc, serviceAccountEntityJSON)

				entityJSON := make(map[string]any)
				if err := json.Unmarshal([]byte(serviceAccountEntityJSON), &entityJSON); err != nil {
					logger.Print(logger.ERROR, "Failed to unmarshal", err)
					return
				}

				getTTLFromResourceName(resourceContext, &resourceContextInsertDoc, entityJSON)

				if envName := GetEnvironmentNameFromValue(serviceAccountName); len(envName) > 0 {
					resourceContextInsertDoc.ResourceEnvTypes.DerivedEnv = append(resourceContextInsertDoc.ResourceEnvTypes.DerivedEnv,
						common.ResourceContextItem{
							Name: envName,
							Type: common.RESOURCE_NAME_ENV_TYPE,
						},
					)
				}

				projectContextID := common.GenerateCombinedHashID(projectID, common.GCP_PROJECT_RESOURCE_TYPE, projectID, resourceContext.LastCollectedAt, resourceContext.TenantID)

				if projectRscCtxInsertDoc, ok := resourceContext.GetResourceContextInsertDoc(projectContextID); ok {
					resourceContextInsertDoc.ResourceOwnerTypes.InheritedOwners = getInheritedOwners(projectRscCtxInsertDoc, common.GCP_PROJECT_RESOURCE_TYPE)
					resourceContextInsertDoc.ResourceEnvTypes.InheritedEnv = getInheritedEnv(projectRscCtxInsertDoc)
				}

				sortUserOverServiceAccount(resourceContextInsertDoc.ResourceOwnerTypes.DefinedOwners)
				resourceContext.SetResourceContextInsertDoc(contextDocID, resourceContextInsertDoc)
			}
		}
	}

	return
}

type ServiceAccountEntity struct {
	ServiceAccountKeys ServiceAccountKey `json:"gcpServiceAccountKey"`
	IAMPolicy          IAMPolicy         `json:"iamPolicy"`
}

type ServiceAccountKey struct {
	Keys []SAKey `json:"keys"`
}

type SAKey struct {
	Name string `json:"name"`
}

type IAMPolicy struct {
	Version      int       `json:"version"`
	Etag         string    `json:"etag"`
	Bindings     []Binding `json:"bindings"`
	AuditConfigs any       `json:"auditConfigs"`
}

type Binding struct {
	Role    string   `json:"role"`
	Members []string `json:"members"`
}

func getServiceAccountIAMPolicies(resourceContext *ResourceContext,
	resourceContextInsertDoc *common.ResourceContextInsertDoc, serviceAccountJSON string) {

	var serviceAccountEntity ServiceAccountEntity

	if err := json.Unmarshal([]byte(serviceAccountJSON), &serviceAccountEntity); err != nil {
		logger.Print(logger.ERROR, "Got error unmarshalling", []string{resourceContext.TenantID, resourceContextInsertDoc.Account}, err)
		return
	}

	var keyCreatorMembers []string

	for _, binding := range serviceAccountEntity.IAMPolicy.Bindings {
		// Do the same for project and org iam policies?

		for _, member := range binding.Members {

			if ok := resourceContext.GetGCPServiceAccountKeyCreatorRoles(strings.ToLower(binding.Role)); ok {
				// For Service Account Key
				keyCreatorMembers = append(keyCreatorMembers, member)
			}

			// For Service Account

			if strings.Contains(member, "serviceAccount:") {
				emailSplit := strings.Split(member, "serviceAccount:")
				if len(emailSplit) > 1 {
					email := emailSplit[1]
					if isServiceAccountEmail(email) {
						serviceAccountName := getGCPServiceAccountName(resourceContext, email, resourceContextInsertDoc.Account)
						resourceContextInsertDoc.ResourceOwnerTypes.DefinedOwners = append(
							resourceContextInsertDoc.ResourceOwnerTypes.DefinedOwners,
							resourceContext.GetUserContextItem(serviceAccountName+SERVICEACCOUNT_USER_SUFFIX, common.POLICYBINDING_USER_TYPE,
								"Service Account has been assigned policy binding for the resource", email, resourceContextInsertDoc.Account, nil),
						)

						uniqueIdentities := make(map[string]struct{})
						getServiceAccountOwnersForPolicy(resourceContext, email, resourceContextInsertDoc.Account, resourceContextInsertDoc, uniqueIdentities,
							"User owned Service Account "+email+" has been assigned policy binding for the resource", 1)
					}
				}
			} else if strings.Contains(member, "user:") {
				emailSplit := strings.Split(member, "user:")
				if len(emailSplit) > 1 {
					emailSplit = strings.Split(emailSplit[1], "?uid")
					email := emailSplit[0]
					resourceContextInsertDoc.ResourceOwnerTypes.DefinedOwners = append(
						resourceContextInsertDoc.ResourceOwnerTypes.DefinedOwners,
						resourceContext.GetUserContextItem(email, common.POLICYBINDING_USER_TYPE,
							"User has been assigned policy binding for the resource", "", resourceContextInsertDoc.Account, nil),
					)
				}
			}
		}
	}

	for _, key := range serviceAccountEntity.ServiceAccountKeys.Keys {

		serviceAccountKeyContextID := common.GenerateCombinedHashID(key.Name, common.GCP_SERVICEACCOUNTKEY_RESOURCE_TYPE, resourceContextInsertDoc.Account, resourceContext.LastCollectedAt, resourceContext.TenantID)

		if len(keyCreatorMembers) <= 3 {

			for _, keyCreatorMember := range keyCreatorMembers {

				if serviceAccountKeyInsertDoc, ok := resourceContext.GetResourceContextInsertDoc(serviceAccountKeyContextID); ok {

					if strings.Contains(keyCreatorMember, "serviceAccount:") {
						emailSplit := strings.Split(keyCreatorMember, "serviceAccount:")
						if len(emailSplit) > 1 {
							email := emailSplit[1]
							if isServiceAccountEmail(email) {
								serviceAccountName := getGCPServiceAccountName(resourceContext, email, resourceContextInsertDoc.Account)
								serviceAccountKeyInsertDoc.ResourceOwnerTypes.DefinedOwners = append(
									serviceAccountKeyInsertDoc.ResourceOwnerTypes.DefinedOwners,
									resourceContext.GetUserContextItem(serviceAccountName+SERVICEACCOUNT_USER_SUFFIX, common.POLICYBINDING_USER_TYPE,
										"Service Account has key creator role for parent Service Account "+resourceContextInsertDoc.ResourceID, email, serviceAccountKeyInsertDoc.Account, nil),
								)

								uniqueIdentities := make(map[string]struct{})
								getServiceAccountOwnersForPolicy(resourceContext, email, serviceAccountKeyInsertDoc.Account, &serviceAccountKeyInsertDoc, uniqueIdentities,
									"User owned Service Account "+email+" has a key creator role for parent Service Account"+resourceContextInsertDoc.ResourceID, 1)
							}
						}
					} else if strings.Contains(keyCreatorMember, "user:") {
						emailSplit := strings.Split(keyCreatorMember, "user:")
						if len(emailSplit) > 1 {
							emailSplit = strings.Split(emailSplit[1], "?uid")
							email := emailSplit[0]
							serviceAccountKeyInsertDoc.ResourceOwnerTypes.DefinedOwners = append(
								serviceAccountKeyInsertDoc.ResourceOwnerTypes.DefinedOwners,
								resourceContext.GetUserContextItem(email, common.POLICYBINDING_USER_TYPE,
									"User has key creator role for parent Service Account "+resourceContextInsertDoc.ResourceID, "", serviceAccountKeyInsertDoc.Account, nil),
							)
						}
					}

					sortUserOverServiceAccount(serviceAccountKeyInsertDoc.ResourceOwnerTypes.DefinedOwners)
					resourceContext.SetResourceContextInsertDoc(serviceAccountKeyContextID, serviceAccountKeyInsertDoc)
				}
			}
		}
	}
}

func GetSAPolicyBindingContext(resourceContext *ResourceContext) {

	logger.Print(logger.INFO, "Processing started for gcp service account policy binding context", []string{resourceContext.TenantID})
	var (
		searchAfter                   any
		saPolicyBindingResourcesQuery = `{"_source":["entityId","accountId","tags","region","entityJson"],"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + resourceContext.TenantID + `"}},{"match":{"entityType.keyword":"` + common.GCP_SAPOLICYBINDING_RESOURCE_TYPE + `"}},{"term":{"collectedAt":` + resourceContext.LastCollectedAt + `}},{"term":{"serviceId":` + common.GCP_SERVICE_ID + `}}]}}}`
	)

	for {
		saPolicyBindingResourcesDocs, sortResponse, err := elastic.ExecuteLargeSearchQuery([]string{elastic.CLOUD_RESOURCES_INDEX}, saPolicyBindingResourcesQuery, searchAfter)
		if err != nil {
			return
		}

		if len(saPolicyBindingResourcesDocs) > 0 {
			searchAfter = sortResponse
		} else {
			break
		}

		var (
			countForCollectedActivityFetch int
			collectedDocIDs                []string
		)

		for saPolicyBindingResourcesDocID, saPolicyBindingResourcesDoc := range saPolicyBindingResourcesDocs {

			var resourceContextInsertDoc common.ResourceContextInsertDoc

			if saPolicyBindingID, ok := saPolicyBindingResourcesDoc["entityId"].(string); ok {

				if projectOrOrgID, ok := saPolicyBindingResourcesDoc["accountId"].(string); ok {

					if region, ok := saPolicyBindingResourcesDoc["region"].(string); ok {

						entityJSONString, _ := saPolicyBindingResourcesDoc["entityJson"].(string)

						saPolicyBindingName := getNameForGCPResource(saPolicyBindingID, common.GCP_SAPOLICYBINDING_RESOURCE_TYPE, entityJSONString)

						contextDocID := common.GenerateCombinedHashID(saPolicyBindingID, common.GCP_SAPOLICYBINDING_RESOURCE_TYPE, projectOrOrgID, resourceContext.LastCollectedAt, resourceContext.TenantID)

						resourceContextInsertDoc = common.ResourceContextInsertDoc{
							ResourceID:         saPolicyBindingID,
							ResourceName:       saPolicyBindingName,
							ResourceType:       common.GCP_SAPOLICYBINDING_RESOURCE_TYPE,
							Account:            projectOrOrgID,
							TenantID:           resourceContext.TenantID,
							Region:             region,
							ServiceID:          common.GCP_SERVICE_ID_INT,
							CloudResourceDocID: saPolicyBindingResourcesDocID,
						}

						setCustomerEntityIncludeContextOfResource(resourceContext, &resourceContextInsertDoc)
						getTagContextOfResource(resourceContext, saPolicyBindingResourcesDoc, &resourceContextInsertDoc)

						entityJSON := make(map[string]any)
						if err := json.Unmarshal([]byte(entityJSONString), &entityJSON); err != nil {
							logger.Print(logger.ERROR, "Failed to unmarshal", err)
							return
						}

						getTTLFromResourceName(resourceContext, &resourceContextInsertDoc, entityJSON)

						if envName := GetEnvironmentNameFromValue(saPolicyBindingName); len(envName) > 0 {
							resourceContextInsertDoc.ResourceEnvTypes.DerivedEnv = append(resourceContextInsertDoc.ResourceEnvTypes.DerivedEnv,
								common.ResourceContextItem{
									Name: envName,
									Type: common.RESOURCE_NAME_ENV_TYPE,
								},
							)
						}

						projectContextID := common.GenerateCombinedHashID(projectOrOrgID, common.GCP_PROJECT_RESOURCE_TYPE, projectOrOrgID, resourceContext.LastCollectedAt, resourceContext.TenantID)

						if projectRscCtxInsertDoc, ok := resourceContext.GetResourceContextInsertDoc(projectContextID); ok {
							resourceContextInsertDoc.ResourceOwnerTypes.InheritedOwners = getInheritedOwners(projectRscCtxInsertDoc, common.GCP_PROJECT_RESOURCE_TYPE)
							resourceContextInsertDoc.ResourceEnvTypes.InheritedEnv = getInheritedEnv(projectRscCtxInsertDoc)
						} else {
							orgContextID := common.GenerateCombinedHashID(projectOrOrgID, common.GCP_ORG_RESOURCE_TYPE, projectOrOrgID, resourceContext.LastCollectedAt, resourceContext.TenantID)
							if orgRscCtxInsertDoc, ok := resourceContext.GetResourceContextInsertDoc(orgContextID); ok {
								resourceContextInsertDoc.ResourceOwnerTypes.InheritedOwners = getInheritedOwners(orgRscCtxInsertDoc, common.GCP_PROJECT_RESOURCE_TYPE)
								resourceContextInsertDoc.ResourceEnvTypes.InheritedEnv = getInheritedEnv(orgRscCtxInsertDoc)
							}
						}

						resourceContext.SetResourceContextInsertDoc(contextDocID, resourceContextInsertDoc)

						countForCollectedActivityFetch++
						collectedDocIDs = append(collectedDocIDs, contextDocID)

						if countForCollectedActivityFetch > 999 {
							getActivityContextOfResource(resourceContext, collectedDocIDs)
							getCodeContextOfResource(resourceContext, collectedDocIDs)
							getJiraContextOfResource(resourceContext, collectedDocIDs)

							countForCollectedActivityFetch = 0
							collectedDocIDs = []string{}
						}
					}
				}
			}
		}

		if countForCollectedActivityFetch > 0 {
			if err = getActivityContextOfResource(resourceContext, collectedDocIDs); err != nil {
				break
			}

			if err = getCodeContextOfResource(resourceContext, collectedDocIDs); err != nil {
				break
			}

			if err = getJiraContextOfResource(resourceContext, collectedDocIDs); err != nil {
				break
			}

			countForCollectedActivityFetch = 0
		}
	}

	logger.Print(logger.INFO, "Processing complete for gcp service account policy binding context", []string{resourceContext.TenantID})
}

func GetFirewallRules(resourceContext *ResourceContext) {
	logger.Print(logger.INFO, "Gathering started of firewall rules", []string{resourceContext.TenantID})

	var (
		searchAfter            any
		firewallResourcesQuery = `{"_source":["entityId","entityJson"],"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + resourceContext.TenantID + `"}},{"match":{"entityType.keyword":"` + common.GCP_FIREWALL_RESOURCE_TYPE + `"}},{"term":{"collectedAt":` + resourceContext.LastCollectedAt + `}},{"term":{"serviceId":` + common.GCP_SERVICE_ID + `}}]}}}`
		firewallResourcesChan  = make(chan map[string]any, MAX_PARENT_THREAD)
		wg                     sync.WaitGroup
		semaphore              = make(chan struct{}, MAX_PARENT_THREAD)
	)

	go func() {
		defer close(firewallResourcesChan)
		for {
			firewallResourcesDocs, sortResponse, err := elastic.ExecuteLargeSearchQuery([]string{elastic.CLOUD_RESOURCES_INDEX}, firewallResourcesQuery, searchAfter)
			if err != nil {
				return
			}

			if len(firewallResourcesDocs) > 0 {
				searchAfter = sortResponse
				for _, doc := range firewallResourcesDocs {
					firewallResourcesChan <- doc
				}
			} else {
				return
			}
		}
	}()

	for firewallResourcesDoc := range firewallResourcesChan {
		semaphore <- struct{}{}
		wg.Add(1)

		go func(doc map[string]any) {
			defer wg.Done()
			defer func() { <-semaphore }()
			processFirewallRule(resourceContext, doc)
		}(firewallResourcesDoc)
	}

	wg.Wait()
	logger.Print(logger.INFO, "Gathering complete of firewall rules", []string{resourceContext.TenantID})
}

func processFirewallRule(resourceContext *ResourceContext, firewallResourcesDoc map[string]any) {

	var networkInboundPorts = make(map[int]struct{})

	if firewallID, ok := firewallResourcesDoc["entityId"].(string); ok {

		if entityJson, ok := firewallResourcesDoc["entityJson"].(string); ok {

			entityJsonMap := make(map[string]any)

			if err := json.Unmarshal([]byte(entityJson), &entityJsonMap); err != nil {
				logger.Print(logger.ERROR, "Failed to unmarshal", err)
				return
			}

			if direction, ok := entityJsonMap["direction"].(string); ok && direction == "INGRESS" {

				if allowedRules, ok := entityJsonMap["allowed"].([]any); ok {

					for _, allowedRule := range allowedRules {

						if allowedRuleJson, ok := allowedRule.(map[string]any); ok {

							fromPort, _ := allowedRuleJson["fromPort"].(float64)
							toPort, _ := allowedRuleJson["toPort"].(float64)

							networkInboundPorts[int(fromPort)] = struct{}{}
							networkInboundPorts[int(toPort)] = struct{}{}
						}
					}

					resourceContext.SetNetworkInboundPorts(firewallID, networkInboundPorts)
				}
			}

			if firewallRuleMap, ok := entityJsonMap["firewallRule"].(map[string]any); ok {
				if network, ok := firewallRuleMap["network"].(string); ok && len(network) > 0 {

					firewallTargets, exists := resourceContext.GetGCPFirewallTargets(network)
					if !exists {
						firewallTargets = make(map[string][]string)
					}

					if targets, ok := entityJsonMap["targets"].(string); ok && len(targets) > 0 {

						if targets == "All instances in the network" {
							if _, ok := firewallTargets["ALL"]; !ok {
								firewallTargets["ALL"] = []string{}
							}

							firewallTargets["ALL"] = append(
								firewallTargets["ALL"],
								firewallID,
							)
						} else if targets == "Specified target tags" {
							if networkTags, ok := firewallRuleMap["targetTags"].([]any); ok {
								for _, networkTag := range networkTags {
									if networkTagString, ok := networkTag.(string); ok && len(networkTagString) > 0 {
										targetKey := "Tag:" + networkTagString

										if _, ok := firewallTargets[targetKey]; !ok {
											firewallTargets[targetKey] = []string{}
										}

										firewallTargets[targetKey] = append(
											firewallTargets[targetKey],
											firewallID,
										)
									}
								}
							}
						} else if targets == "Specified service account" {

							if serviceAccounts, ok := firewallRuleMap["targetServiceAccounts"].([]any); ok {
								for _, serviceAccount := range serviceAccounts {
									if serviceAccountString, ok := serviceAccount.(string); ok && len(serviceAccountString) > 0 {
										targetKey := "SA:" + serviceAccountString

										if _, ok := firewallTargets[targetKey]; !ok {
											firewallTargets[targetKey] = []string{}
										}

										firewallTargets[targetKey] = append(
											firewallTargets[targetKey],
											firewallID,
										)
									}
								}
							}
						}
					}

					resourceContext.SetGCPFirewallTargets(network, firewallTargets)
				}
			}
		}
	}
}

func getGCPServiceAccountName(resourceContext *ResourceContext, serviceAccount, defaultProject string) (serviceAccountName string) {

	serviceAccountName = serviceAccount

	saDoc := getServiceAccountDoc(resourceContext, serviceAccount, defaultProject)

	if len(saDoc.ResourceName) > 0 {
		serviceAccountName = saDoc.ResourceName
	}

	return
}

func getServiceAccountDoc(resourceContext *ResourceContext, serviceAccount, defaultProject string) (saDoc common.ResourceContextInsertDoc) {

	projectID, _ := parseServiceAccountData(serviceAccount, defaultProject, resourceContext)
	serviceAccountContextID := common.GenerateCombinedHashID(serviceAccount, common.GCP_SERVICEACCOUNT_RESOURCE_TYPE, projectID, resourceContext.LastCollectedAt, resourceContext.TenantID)

	saDoc, ok := resourceContext.GetResourceContextInsertDoc(serviceAccountContextID)
	if !ok {
		serviceAccountContextID = common.GenerateCombinedHashID(serviceAccount, common.GCP_SAPOLICYBINDING_RESOURCE_TYPE, defaultProject, resourceContext.LastCollectedAt, resourceContext.TenantID)
		if saDoc, ok = resourceContext.GetResourceContextInsertDoc(serviceAccountContextID); !ok {
			if orgID, ok := resourceContext.GetGCPProjectOrg(defaultProject); ok {
				serviceAccountContextID = common.GenerateCombinedHashID(serviceAccount, common.GCP_SAPOLICYBINDING_RESOURCE_TYPE, orgID, resourceContext.LastCollectedAt, resourceContext.TenantID)
				if saDoc, ok = resourceContext.GetResourceContextInsertDoc(serviceAccountContextID); !ok {
					return
				}
			}
		}
	}

	return
}

func sortUserOverServiceAccount(slice []common.ResourceContextItem) {
	sort.SliceStable(slice, func(i, j int) bool {
		isServiceAccountI := strings.HasSuffix(slice[i].Name, SERVICEACCOUNT_USER_SUFFIX)
		isServiceAccountJ := strings.HasSuffix(slice[j].Name, SERVICEACCOUNT_USER_SUFFIX)

		if isServiceAccountI != isServiceAccountJ {
			return !isServiceAccountI
		}

		return false
	})
}

func getHierarchicalContext(contextDocID string, resourceContext *ResourceContext, resourceContextInsertDoc *common.ResourceContextInsertDoc) {

	if len(resourceContextInsertDoc.Account) > 0 {

		var (
			hierarchicalParent   = resourceContextInsertDoc.Account
			closestParentContext = struct{ inheritedOwner, ownerRole, env, team bool }{}
		)

		for _, definedOwner := range resourceContextInsertDoc.ResourceOwnerTypes.DefinedOwners {
			if definedOwner.Type == common.FOLDER_OWNER_USER_TYPE || definedOwner.Type == common.PROJECT_OWNER_USER_TYPE {
				// Already got owner role from the resource itself. No need to go to closest parent
				closestParentContext.ownerRole = true
			}
		}

		for i := 0; i < 20; i++ { // To find org of project - If not reaching org in 20 hierarchies (approx max limit), exit

			folderContextID := common.GenerateCombinedHashID(hierarchicalParent, common.GCP_FOLDER_RESOURCE_TYPE, hierarchicalParent, resourceContext.LastCollectedAt, resourceContext.TenantID)

			if folderRscCtxInsertDoc, ok := resourceContext.GetResourceContextInsertDoc(folderContextID); ok {

				if i == 0 {
					assignRelatedResource(resourceContext, resourceContextInsertDoc.ResourceID, contextDocID, resourceContextInsertDoc.ResourceType,
						resourceContextInsertDoc.Account, folderContextID, common.GCP_FOLDER_RESOURCE_TYPE, false)
				}

				if !closestParentContext.inheritedOwner {
					resourceContextInsertDoc.ResourceOwnerTypes.InheritedOwners = getInheritedOwners(folderRscCtxInsertDoc, common.GCP_FOLDER_RESOURCE_TYPE)
					if len(resourceContextInsertDoc.ResourceOwnerTypes.InheritedOwners) > 0 {
						closestParentContext.inheritedOwner = true
					}
				}

				if !closestParentContext.ownerRole {
					// GCP users with owner role in folder, has owner role in child folders/projects
					if folderOwners, ok := resourceContext.GetGCPParentOwners(hierarchicalParent); ok {
						for _, owner := range folderOwners {
							resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners = append(
								resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners,
								resourceContext.GetUserContextItem(owner, common.PARENTFOLDER_OWNER_USER_TYPE, "", "", resourceContextInsertDoc.Account, nil),
							)

							closestParentContext.ownerRole = true
						}
					}
				}

				if !closestParentContext.env {
					resourceContextInsertDoc.ResourceEnvTypes.InheritedEnv = getInheritedEnv(folderRscCtxInsertDoc)
					if len(resourceContextInsertDoc.ResourceEnvTypes.InheritedEnv) > 0 {
						closestParentContext.env = true
					}
				}

				if !closestParentContext.team {
					resourceContextInsertDoc.ResourceTeamTypes.InheritedTeam = getInheritedTeam(folderRscCtxInsertDoc)
					if len(resourceContextInsertDoc.ResourceTeamTypes.InheritedTeam) > 0 {
						closestParentContext.team = true
					}
				}

				hierarchicalParent = folderRscCtxInsertDoc.Account

			} else {

				orgContextID := common.GenerateCombinedHashID(hierarchicalParent, common.GCP_ORG_RESOURCE_TYPE, hierarchicalParent, resourceContext.LastCollectedAt, resourceContext.TenantID)

				if orgRscCtxInsertDoc, ok := resourceContext.GetResourceContextInsertDoc(orgContextID); ok {

					if resourceContextInsertDoc.ResourceType == common.GCP_PROJECT_RESOURCE_TYPE {
						resourceContext.SetGCPProjectOrg(resourceContextInsertDoc.ResourceID, hierarchicalParent)
					}

					if i == 0 {
						assignRelatedResource(resourceContext, resourceContextInsertDoc.ResourceID, contextDocID, resourceContextInsertDoc.ResourceType,
							resourceContextInsertDoc.Account, orgContextID, common.GCP_ORG_RESOURCE_TYPE, false)
					}

					if !closestParentContext.inheritedOwner {
						resourceContextInsertDoc.ResourceOwnerTypes.InheritedOwners = getInheritedOwners(orgRscCtxInsertDoc, common.GCP_ORG_RESOURCE_TYPE)
						if len(resourceContextInsertDoc.ResourceOwnerTypes.InheritedOwners) > 0 {
							closestParentContext.inheritedOwner = true
						}
					}

					if !closestParentContext.ownerRole {
						// GCP users with owner role in org, has owner role in child folders/projects
						if orgOwners, ok := resourceContext.GetGCPParentOwners(hierarchicalParent); ok {
							for _, owner := range orgOwners {
								resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners = append(
									resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners,
									resourceContext.GetUserContextItem(owner, common.ORG_OWNER_USER_TYPE, "", "", orgRscCtxInsertDoc.Account, nil),
								)

								closestParentContext.ownerRole = true
							}
						}
					}

					if !closestParentContext.env {
						resourceContextInsertDoc.ResourceEnvTypes.InheritedEnv = getInheritedEnv(orgRscCtxInsertDoc)
						if len(resourceContextInsertDoc.ResourceEnvTypes.InheritedEnv) > 0 {
							closestParentContext.env = true
						}
					}
				}

				break
			}
		}
	}
}
