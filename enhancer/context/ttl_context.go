package context

import (
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/precize/common"
	"github.com/precize/elastic"
	"github.com/precize/logger"
)

func IsTTLKey(tagKey string) bool {

	for ttlKey := range TTLTagKeys {

		if strings.Contains(ttlKey, `\b`) {
			// \b whole word match does not consider _ as special character
			tagKey = strings.ReplaceAll(tagKey, "_", "-")
		}

		regex := regexp.MustCompile(ttlKey)
		if regex.MatchString(strings.ToLower(tagKey)) {
			return true
		}
	}

	return false
}

func getTTLFromResourceName(resourceContext *ResourceContext, resourceContextInsertDoc *common.ResourceContextInsertDoc, entityJSON map[string]any) {

	ttlValue := GetTTLFromValue(resourceContextInsertDoc.ResourceName, false, getCreateTimeOfResource(entityJSON), resourceContext.LastCollectedAt)

	if len(ttlValue) > 0 {
		resourceContextInsertDoc.ResourceTTLTypes.DerivedTTL = append(resourceContextInsertDoc.ResourceTTLTypes.DerivedTTL,
			common.ResourceContextItem{
				Name: ttlValue,
				Type: common.RESOURCE_NAME_TTL_TYPE,
			})
	}
}

func GetTTLFromValue(str string, tag bool, createTime time.Time, lastCollectedAt string) (ttlValue string) {

	if tag {
		if t, ok := common.TryParseTime(str); ok {
			ttlValue = elastic.DateTime(t)
			return
		}
	}

	ttlValue = GetTTLFromNonTimeValue(strings.ToLower(str), createTime, lastCollectedAt)

	if tag && len(ttlValue) == 0 {
		logger.Print(logger.INFO, "Unknown TTL value", str)
	}

	return
}

func GetTTLFromNonTimeValue(str string, createTime time.Time, lastCollectedAt string) (ttl string) {

	for ttlValue := range TTLDeleteValues {

		if strings.Contains(ttlValue, `\b`) {
			// \b whole word match does not consider _ as special character
			str = strings.ReplaceAll(str, "_", "-")
		}

		regex := regexp.MustCompile(ttlValue)
		if regex.MatchString(str) {

			if !createTime.IsZero() {
				// Two weeks from create time
				ttl = elastic.DateTime(createTime.Add(14 * 24 * time.Hour))

				return
			} else {

				if lastCollected, err := strconv.ParseInt(lastCollectedAt, 10, 64); err == nil {

					// lastCollectedAt is epoch millis
					// One second before lastCollectedAt
					ttl = elastic.DateTime(time.UnixMilli(lastCollected - 1000))

					return
				}
			}
		}
	}

	for ttlValue := range TTLDoNotDeleteValues {

		if strings.Contains(ttlValue, `\b`) {
			// \b whole word match does not consider _ as special character
			str = strings.ReplaceAll(str, "_", "-")
		}

		regex := regexp.MustCompile(ttlValue)
		if regex.MatchString(str) {
			ttl = elastic.DateTime(common.MAX_TIME)
			return
		}
	}

	if str == "-1" {
		ttl = elastic.DateTime(common.MAX_TIME)
		return
	}

	return
}

func GetUniqueTTLContext(resourceContextDoc *common.ResourceContextInsertDoc) (ttl []string) {

	uniqueTTL := make(map[string]struct{})

	getUniqueContext(&resourceContextDoc.DefinedTTL, uniqueTTL)
	getUniqueContext(&resourceContextDoc.DerivedTTL, uniqueTTL)
	getUniqueContext(&resourceContextDoc.InheritedTTL, uniqueTTL)

	for ttlValue := range uniqueTTL {
		ttl = append(ttl, ttlValue)
	}

	return
}
