package context

import (
	"regexp"
	"strings"

	"slices"

	"github.com/precize/common"
)

func GetDeploymentNamesFromValue(str string) (deployments []string) {

	str = strings.ToLower(str)

	for deployment, values := range deploymentKeyOrValues {
		for _, val := range values {

			if strings.Contains(val, `\b`) {
				// \b whole word match does not consider _ as special character
				str = strings.ReplaceAll(str, "_", "-")
			}

			regex := regexp.MustCompile(val)
			if regex.MatchString(str) {
				deployments = append(deployments, deployment)
			}
		}
	}

	return
}

func GetUniqueDeploymentContext(resourceContextDoc *common.ResourceContextInsertDoc) (deployment []string) {

	uniqueDeployment := make(map[string]struct{})

	getUniqueContext(&resourceContextDoc.DefinedDeployment, uniqueDeployment)
	getUniqueContext(&resourceContextDoc.DerivedDeployment, uniqueDeployment)

	combinedDeployment(uniqueDeployment, resourceContextDoc)

	for deploymentName := range uniqueDeployment {
		deployment = append(deployment, deploymentName)
	}

	return
}

func combinedDeployment(uniqueDeployment map[string]struct{}, resourceContextDoc *common.ResourceContextInsertDoc) {

	deleteDeployment := func(deploymentName string) {
		delete(uniqueDeployment, deploymentName)

		resourceContextDoc.ResourceDeploymentTypes.DefinedDeployment = filterResourceContextItems(
			resourceContextDoc.ResourceDeploymentTypes.DefinedDeployment, deploymentName,
		)

		resourceContextDoc.ResourceDeploymentTypes.DerivedDeployment = filterResourceContextItems(
			resourceContextDoc.ResourceDeploymentTypes.DerivedDeployment, deploymentName,
		)
	}

	// If any of the IaC deployments are present, remove all other deployments
	iacDeployments := []string{TERRAFORM_DEPLOYMENT, CFT_DEPLOYMENT, AZDO_TF_DEPLOYMENT}
	hasIaC := false

	for _, iacDeployment := range iacDeployments {
		if _, ok := uniqueDeployment[iacDeployment]; ok {
			hasIaC = true
			break
		}
	}

	if hasIaC {
		for _, item := range resourceContextDoc.ResourceDeploymentTypes.DefinedDeployment {
			isIaC := slices.Contains(iacDeployments, item.Name)
			if !isIaC {
				resourceContextDoc.ResourceDeploymentTypes.DefinedDeployment = filterResourceContextItems(
					resourceContextDoc.ResourceDeploymentTypes.DefinedDeployment, item.Name,
				)
				delete(uniqueDeployment, item.Name)
			}
		}

		for _, item := range resourceContextDoc.ResourceDeploymentTypes.DerivedDeployment {
			isIaC := slices.Contains(iacDeployments, item.Name)
			if !isIaC {
				resourceContextDoc.ResourceDeploymentTypes.DerivedDeployment = filterResourceContextItems(
					resourceContextDoc.ResourceDeploymentTypes.DerivedDeployment, item.Name,
				)
				delete(uniqueDeployment, item.Name)
			}
		}

		return
	}

	if _, ok := uniqueDeployment[AZDO_TF_DEPLOYMENT]; ok {
		deleteDeployment(AZDO_DEPLOYMENT)
		deleteDeployment(TERRAFORM_DEPLOYMENT)
	}

	if _, ok := uniqueDeployment[KUBERNETES_DEPLOYMENT]; ok {
		if _, ok := uniqueDeployment[ARGO_DEPLOYMENT]; ok {
			uniqueDeployment[ARGO_KUBERNETES_DEPLOYMENT] = struct{}{}

			for _, rctxItem := range resourceContextDoc.ResourceDeploymentTypes.DefinedDeployment {
				if rctxItem.Name == ARGO_DEPLOYMENT {
					resourceContextDoc.ResourceDeploymentTypes.DefinedDeployment = append(
						resourceContextDoc.ResourceDeploymentTypes.DefinedDeployment,
						common.ResourceContextItem{
							Name: ARGO_KUBERNETES_DEPLOYMENT,
							Type: rctxItem.Type,
						},
					)
				}
			}

			for _, rctxItem := range resourceContextDoc.ResourceDeploymentTypes.DerivedDeployment {
				if rctxItem.Name == ARGO_DEPLOYMENT {
					resourceContextDoc.ResourceDeploymentTypes.DerivedDeployment = append(
						resourceContextDoc.ResourceDeploymentTypes.DerivedDeployment,
						common.ResourceContextItem{
							Name: ARGO_KUBERNETES_DEPLOYMENT,
							Type: rctxItem.Type,
						},
					)
				}
			}

			deleteDeployment(KUBERNETES_DEPLOYMENT)
			deleteDeployment(ARGO_DEPLOYMENT)
		}
	}
}

func filterResourceContextItems(items []common.ResourceContextItem, nameToRemove string) []common.ResourceContextItem {
	var filtered []common.ResourceContextItem
	for _, item := range items {
		if item.Name != nameToRemove {
			filtered = append(filtered, item)
		}
	}
	return filtered
}
