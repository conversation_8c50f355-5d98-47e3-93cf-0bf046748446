package context

import (
	"encoding/json"
	"slices"
	"strconv"
	"strings"

	"github.com/precize/common"
	"github.com/precize/elastic"
	"github.com/precize/logger"
)

const (
	ASSET_CONTEXT_TYPE    = "asset"
	IDENTITY_CONTEXT_TYPE = "identity"
	ORG_CONTEXT_TYPE      = "org"

	OWNER_PROPERTY_NAME       = "owner"
	ENVIRONMENT_PROPERTY_NAME = "environment"
	APP_PROPERTY_NAME         = "app"
	SOFTWARE_PROPERTY_NAME    = "software"
	SENSITIVITY_PROPERTY_NAME = "sensitivity"
	COMPLIANCE_PROPERTY_NAME  = "compliance"
	DEPLOYMENT_PROPERTY_NAME  = "deployment"
	TTL_PROPERTY_NAME         = "ttl"
	COSTCENTER_PROPERTY_NAME  = "costcenter"
	TEAM_PROPERTY_NAME        = "team"
	USER_AGENT_PROPERTY_NAME  = "userAgent"

	IDENTITYNAME_PROPERTY_NAME    = "name"
	EXUSER_PROPERTY_NAME          = "exUser"
	PARTNER_PROPERTY_NAME         = "isPartner"
	IDENTITY_STATUS_PROPERTY_NAME = "identityStatus"
	EMAIL_PROPERTY_NAME           = "email"
	DOMAIN_PROPERTY_NAME          = "domain"
	PARTNER_EMAIL_PROPERTY_NAME   = "partnerEmail"

	DOMAINPROPERTY_HUMAN_VALUE     = "Human"
	DOMAINPROPERTY_NON_HUMAN_VALUE = "Non-Human"

	EMAILSTATUSPROPERTY_DELIVERABLE_VALUE   = "deliverable"
	EMAILSTATUSPROPERTY_UNDELIVERABLE_VALUE = "undeliverable"
)

func GetCustomerDefinedContexts(resourceContext *ResourceContext) {

	var (
		searchAfter              any
		customerDefinitionsQuery = `{"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + resourceContext.TenantID + `"}}]}}}`
	)

	for {
		customerDefinitionsDocs, sortResponse, err := elastic.ExecuteLargeSearchQuery([]string{elastic.CUST_ENTITY_CONTEXT_INDEX}, customerDefinitionsQuery, searchAfter)
		if err != nil {
			return
		}

		if len(customerDefinitionsDocs) > 0 {
			searchAfter = sortResponse
		} else {
			break
		}

		for _, customerDefinitionsDoc := range customerDefinitionsDocs {

			customerDefinitionsJson, err := json.Marshal(customerDefinitionsDoc)
			if err != nil {
				logger.Print(logger.ERROR, "Failed to marshal", err)
				continue
			}

			var customerEntityContext common.CustomerEntityContextDoc

			if err = json.Unmarshal(customerDefinitionsJson, &customerEntityContext); err != nil {
				logger.Print(logger.ERROR, "Failed to unmarshal", err)
				continue
			}

			switch customerEntityContext.Type {
			case ASSET_CONTEXT_TYPE:
				if strconv.Itoa(customerEntityContext.ServiceID) != resourceContext.ServiceID {
					continue
				}
				resourceContext.SetCustomerEntityContext(customerEntityContext.EntityID+customerEntityContext.EntityType, customerEntityContext)
			case IDENTITY_CONTEXT_TYPE:
				processIdentityContext(resourceContext, customerEntityContext)
			}
		}
	}
}

func setCustomerEntityIncludeContextOfResource(resourceContext *ResourceContext, resourceContextInsertDoc *common.ResourceContextInsertDoc) {

	var (
		uniqueOwners        = make(map[string]struct{})
		uniqueApps          = make(map[string]struct{})
		uniqueSoftware      = make(map[string]struct{})
		uniqueCompliances   = make(map[string]struct{})
		uniqueSensitivities = make(map[string]struct{})
		uniqueEnvironments  = make(map[string]struct{})
		uniqueDeployments   = make(map[string]struct{})
		uniqueCostCenters   = make(map[string]struct{})
		uniqueTeams         = make(map[string]struct{})
		uniqueTTL           = make(map[string]struct{})
	)

	if customerEntityContext, ok := resourceContext.GetCustomerEntityContext(resourceContextInsertDoc.ResourceID + resourceContextInsertDoc.ResourceType); ok {

		updateBy := customerEntityContext.UpdatedBy

		for _, contextProperty := range customerEntityContext.ContextProperties {

			for _, includedProperty := range contextProperty.Include {

				switch contextProperty.PropertyName {

				case OWNER_PROPERTY_NAME:

					if _, ok := uniqueOwners[strings.ToLower(includedProperty)]; !ok {

						uniqueOwners[strings.ToLower(includedProperty)] = struct{}{}

						userType := common.CUSTOMER_DEFINED_USER_TYPE
						desc := GetStaticDescriptionOfUserType(userType) + " by " + updateBy
						if strings.HasPrefix(updateBy, "support_") {
							userType = common.PRECIZE_DEFINED_USER_TYPE
							desc = ""
						}

						resourceContextInsertDoc.ResourceOwnerTypes.DefinedOwners = append(
							resourceContextInsertDoc.ResourceOwnerTypes.DefinedOwners,
							resourceContext.GetUserContextItem(includedProperty, userType, desc, "", resourceContextInsertDoc.Account, nil),
						)
					}

				case ENVIRONMENT_PROPERTY_NAME:
					if _, ok := uniqueEnvironments[strings.ToLower(includedProperty)]; !ok {

						uniqueEnvironments[strings.ToLower(includedProperty)] = struct{}{}

						envType := common.CUSTOMER_DEFINED_ENV_TYPE
						if strings.HasPrefix(updateBy, "support_") {
							envType = common.PRECIZE_DEFINED_ENV_TYPE
						}

						resourceContextInsertDoc.ResourceEnvTypes.DefinedEnv = append(
							resourceContextInsertDoc.ResourceEnvTypes.DefinedEnv,
							common.ResourceContextItem{
								Name: FormatContextValue(includedProperty),
								Type: envType,
							},
						)
					}

				case APP_PROPERTY_NAME:

					if _, ok := uniqueApps[strings.ToLower(includedProperty)]; !ok {

						uniqueApps[strings.ToLower(includedProperty)] = struct{}{}

						appType := common.CUSTOMER_DEFINED_APP_TYPE
						if strings.HasPrefix(updateBy, "support_") {
							appType = common.PRECIZE_DEFINED_APP_TYPE
						}

						resourceContextInsertDoc.ResourceAppTypes.DefinedApp = append(
							resourceContextInsertDoc.ResourceAppTypes.DefinedApp,
							common.ResourceContextItem{
								Name: FormatContextValue(includedProperty),
								Type: appType,
							},
						)
					}

				case SOFTWARE_PROPERTY_NAME:

					if _, ok := uniqueSoftware[strings.ToLower(includedProperty)]; !ok {

						uniqueSoftware[strings.ToLower(includedProperty)] = struct{}{}

						softwareType := common.CUSTOMER_DEFINED_SOFTWARE_TYPE
						if strings.HasPrefix(updateBy, "support_") {
							softwareType = common.PRECIZE_DEFINED_SOFTWARE_TYPE
						}

						resourceContextInsertDoc.ResourceSoftwareTypes.DefinedSoftware = append(
							resourceContextInsertDoc.ResourceSoftwareTypes.DefinedSoftware,
							common.ResourceContextItem{
								Name: FormatContextValue(includedProperty),
								Type: softwareType,
							},
						)
					}

				case SENSITIVITY_PROPERTY_NAME:

					if _, ok := uniqueSensitivities[strings.ToLower(includedProperty)]; !ok {

						uniqueSensitivities[strings.ToLower(includedProperty)] = struct{}{}

						sensitityType := common.CUSTOMER_DEFINED_SENSITIVITY_TYPE
						if strings.HasPrefix(updateBy, "support_") {
							sensitityType = common.PRECIZE_DEFINED_SENSITIVITY_TYPE
						}

						resourceContextInsertDoc.ResourceSensitivityTypes.DefinedSensitivity = append(
							resourceContextInsertDoc.ResourceSensitivityTypes.DefinedSensitivity,
							common.ResourceContextItem{
								Name: FormatContextValue(includedProperty),
								Type: sensitityType,
							},
						)
					}

				case COMPLIANCE_PROPERTY_NAME:

					if _, ok := uniqueCompliances[strings.ToLower(includedProperty)]; !ok {

						uniqueCompliances[strings.ToLower(includedProperty)] = struct{}{}

						complianceType := common.CUSTOMER_DEFINED_COMPLIANCE_TYPE
						if strings.HasPrefix(updateBy, "support_") {
							complianceType = common.PRECIZE_DEFINED_COMPLIANCE_TYPE
						}

						resourceContextInsertDoc.ResourceComplianceTypes.DefinedCompliance = append(
							resourceContextInsertDoc.ResourceComplianceTypes.DefinedCompliance,
							common.ResourceContextItem{
								Name: FormatContextValue(includedProperty),
								Type: complianceType,
							},
						)
					}

				case DEPLOYMENT_PROPERTY_NAME:

					if _, ok := uniqueDeployments[strings.ToLower(includedProperty)]; !ok {

						uniqueDeployments[strings.ToLower(includedProperty)] = struct{}{}

						deploymentType := common.CUSTOMER_DEFINED_DEPLOYMENT_TYPE
						if strings.HasPrefix(updateBy, "support_") {
							deploymentType = common.PRECIZE_DEFINED_DEPLOYMENT_TYPE
						}

						resourceContextInsertDoc.ResourceDeploymentTypes.DefinedDeployment = append(
							resourceContextInsertDoc.ResourceDeploymentTypes.DefinedDeployment,
							common.ResourceContextItem{
								Name: FormatContextValue(includedProperty),
								Type: deploymentType,
							},
						)
					}

				case COSTCENTER_PROPERTY_NAME:

					if _, ok := uniqueCostCenters[strings.ToLower(includedProperty)]; !ok {

						uniqueCostCenters[strings.ToLower(includedProperty)] = struct{}{}

						costCenterType := common.CUSTOMER_DEFINED_COSTCENTER_TYPE
						if strings.HasPrefix(updateBy, "support_") {
							costCenterType = common.PRECIZE_DEFINED_COSTCENTER_TYPE
						}

						resourceContextInsertDoc.ResourceCostCenterTypes.DefinedCostCenter = append(
							resourceContextInsertDoc.ResourceCostCenterTypes.DefinedCostCenter,
							common.ResourceContextItem{
								Name: FormatContextValue(includedProperty),
								Type: costCenterType,
							},
						)
					}

				case TEAM_PROPERTY_NAME:

					if _, ok := uniqueTeams[strings.ToLower(includedProperty)]; !ok {

						uniqueTeams[strings.ToLower(includedProperty)] = struct{}{}

						teamType := common.CUSTOMER_DEFINED_TEAM_TYPE
						if strings.HasPrefix(updateBy, "support_") {
							teamType = common.PRECIZE_DEFINED_TEAM_TYPE
						}

						resourceContextInsertDoc.ResourceTeamTypes.DefinedTeam = append(
							resourceContextInsertDoc.ResourceTeamTypes.DefinedTeam,
							common.ResourceContextItem{
								Name: FormatContextValue(includedProperty),
								Type: teamType,
							},
						)
					}

				case TTL_PROPERTY_NAME:

					if _, ok := uniqueTTL[strings.ToLower(includedProperty)]; !ok {

						uniqueTTL[strings.ToLower(includedProperty)] = struct{}{}

						ttlType := common.CUSTOMER_DEFINED_TTL_TYPE
						if strings.HasPrefix(updateBy, "support_") {
							ttlType = common.PRECIZE_DEFINED_TTL_TYPE
						}

						resourceContextInsertDoc.ResourceTTLTypes.DefinedTTL = append(
							resourceContextInsertDoc.ResourceTTLTypes.DefinedTTL,
							common.ResourceContextItem{
								Name: FormatContextValue(includedProperty),
								Type: ttlType,
							},
						)
					}
				}
			}
		}
	}
}

func RemoveCustomerEntityExcludeContextOfResource(resourceContext *ResourceContext, resourceContextInsertDoc *common.ResourceContextInsertDoc, uniqueMap map[string][]string) {

	if customerEntityContext, ok := resourceContext.GetCustomerEntityContext(resourceContextInsertDoc.ResourceID + resourceContextInsertDoc.ResourceType); ok {

		for _, contextProperty := range customerEntityContext.ContextProperties {

			var excludedList = make(map[string]struct{})

			for _, value := range contextProperty.Exclude {
				excludedList[FormatContextValue(value)] = struct{}{}
			}

			uniquePropertyValues := uniqueMap[contextProperty.PropertyName]

			for i := len(uniquePropertyValues) - 1; i >= 0; i-- {
				if _, ok := excludedList[uniquePropertyValues[i]]; ok {
					uniquePropertyValues = slices.Delete(uniquePropertyValues, i, i+1)
				}
			}

			uniqueMap[contextProperty.PropertyName] = uniquePropertyValues

			switch contextProperty.PropertyName {
			case OWNER_PROPERTY_NAME:

				resourceContextInsertDoc.ResourceOwnerTypes.DefinedOwners = deleteExcludedContext(
					resourceContextInsertDoc.ResourceOwnerTypes.DefinedOwners, excludedList)

				resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners = deleteExcludedContext(
					resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners, excludedList)

				resourceContextInsertDoc.ResourceOwnerTypes.InheritedOwners = deleteExcludedContext(
					resourceContextInsertDoc.ResourceOwnerTypes.InheritedOwners, excludedList)

				resourceContextInsertDoc.ResourceOwnerTypes.CodeOwners = deleteExcludedContext(
					resourceContextInsertDoc.ResourceOwnerTypes.CodeOwners, excludedList)

				resourceContextInsertDoc.ResourceOwnerTypes.CostOwners = deleteExcludedContext(
					resourceContextInsertDoc.ResourceOwnerTypes.CostOwners, excludedList)

				resourceContextInsertDoc.ResourceOwnerTypes.SecurityOwners = deleteExcludedContext(
					resourceContextInsertDoc.ResourceOwnerTypes.SecurityOwners, excludedList)

				resourceContextInsertDoc.ResourceOwnerTypes.OpsOwners = deleteExcludedContext(
					resourceContextInsertDoc.ResourceOwnerTypes.OpsOwners, excludedList)

			case ENVIRONMENT_PROPERTY_NAME:

				resourceContextInsertDoc.ResourceEnvTypes.DefinedEnv = deleteExcludedContext(
					resourceContextInsertDoc.ResourceEnvTypes.DefinedEnv, excludedList)

				resourceContextInsertDoc.ResourceEnvTypes.DerivedEnv = deleteExcludedContext(
					resourceContextInsertDoc.ResourceEnvTypes.DerivedEnv, excludedList)

				resourceContextInsertDoc.ResourceEnvTypes.InheritedEnv = deleteExcludedContext(
					resourceContextInsertDoc.ResourceEnvTypes.InheritedEnv, excludedList)

			case APP_PROPERTY_NAME:

				resourceContextInsertDoc.ResourceAppTypes.DefinedApp = deleteExcludedContext(
					resourceContextInsertDoc.ResourceAppTypes.DefinedApp, excludedList)

				resourceContextInsertDoc.ResourceAppTypes.DerivedApp = deleteExcludedContext(
					resourceContextInsertDoc.ResourceAppTypes.DerivedApp, excludedList)

			case SOFTWARE_PROPERTY_NAME:

				resourceContextInsertDoc.ResourceSoftwareTypes.DefinedSoftware = deleteExcludedContext(
					resourceContextInsertDoc.ResourceSoftwareTypes.DefinedSoftware, excludedList)

				resourceContextInsertDoc.ResourceSoftwareTypes.DerivedSoftware = deleteExcludedContext(
					resourceContextInsertDoc.ResourceSoftwareTypes.DerivedSoftware, excludedList)

			case SENSITIVITY_PROPERTY_NAME:

				resourceContextInsertDoc.ResourceSensitivityTypes.DefinedSensitivity = deleteExcludedContext(
					resourceContextInsertDoc.ResourceSensitivityTypes.DefinedSensitivity, excludedList)

				resourceContextInsertDoc.ResourceSensitivityTypes.DerivedSensitivity = deleteExcludedContext(
					resourceContextInsertDoc.ResourceSensitivityTypes.DerivedSensitivity, excludedList)

				resourceContextInsertDoc.ResourceSensitivityTypes.InheritedSensitivity = deleteExcludedContext(
					resourceContextInsertDoc.ResourceSensitivityTypes.InheritedSensitivity, excludedList)

			case COMPLIANCE_PROPERTY_NAME:

				resourceContextInsertDoc.ResourceComplianceTypes.DefinedCompliance = deleteExcludedContext(
					resourceContextInsertDoc.ResourceComplianceTypes.DefinedCompliance, excludedList)

				resourceContextInsertDoc.ResourceComplianceTypes.DerivedCompliance = deleteExcludedContext(
					resourceContextInsertDoc.ResourceComplianceTypes.DerivedCompliance, excludedList)

				resourceContextInsertDoc.ResourceComplianceTypes.InheritedCompliance = deleteExcludedContext(
					resourceContextInsertDoc.ResourceComplianceTypes.InheritedCompliance, excludedList)

			case DEPLOYMENT_PROPERTY_NAME:

				resourceContextInsertDoc.ResourceDeploymentTypes.DefinedDeployment = deleteExcludedContext(
					resourceContextInsertDoc.ResourceDeploymentTypes.DefinedDeployment, excludedList)

				resourceContextInsertDoc.ResourceDeploymentTypes.DerivedDeployment = deleteExcludedContext(
					resourceContextInsertDoc.ResourceDeploymentTypes.DerivedDeployment, excludedList)

			case COSTCENTER_PROPERTY_NAME:

				resourceContextInsertDoc.ResourceCostCenterTypes.DefinedCostCenter = deleteExcludedContext(
					resourceContextInsertDoc.ResourceCostCenterTypes.DefinedCostCenter, excludedList)

				resourceContextInsertDoc.ResourceCostCenterTypes.InheritedCostCenter = deleteExcludedContext(
					resourceContextInsertDoc.ResourceCostCenterTypes.InheritedCostCenter, excludedList)

			case TEAM_PROPERTY_NAME:

				resourceContextInsertDoc.ResourceTeamTypes.DefinedTeam = deleteExcludedContext(
					resourceContextInsertDoc.ResourceTeamTypes.DefinedTeam, excludedList)

				resourceContextInsertDoc.ResourceTeamTypes.DerivedTeam = deleteExcludedContext(
					resourceContextInsertDoc.ResourceTeamTypes.DerivedTeam, excludedList)

			case TTL_PROPERTY_NAME:

				resourceContextInsertDoc.ResourceTTLTypes.DefinedTTL = deleteExcludedContext(
					resourceContextInsertDoc.ResourceTTLTypes.DefinedTTL, excludedList)

				resourceContextInsertDoc.ResourceTTLTypes.DerivedTTL = deleteExcludedContext(
					resourceContextInsertDoc.ResourceTTLTypes.DerivedTTL, excludedList)
			}

		}
	}
}

func deleteExcludedContext(resourceContextItems []common.ResourceContextItem, excludedList map[string]struct{}) (items []common.ResourceContextItem) {

	for _, v := range resourceContextItems {
		if _, ok := excludedList[v.Name]; !ok {
			items = append(items, v)
		}
	}

	return
}

func processIdentityContext(resourceContext *ResourceContext, customerEntityContext common.CustomerEntityContextDoc) {

	if customerEntityContext.EntityID == "" {
		return
	}

	identityID := customerEntityContext.EntityID

	for _, contextProperty := range customerEntityContext.ContextProperties {

		if contextProperty.PropertyName == DOMAIN_PROPERTY_NAME {

			for _, value := range contextProperty.Include {
				if value == DOMAINPROPERTY_HUMAN_VALUE {
					resourceContext.SetAliasUpdate(identityID, false)
				} else if value == DOMAINPROPERTY_NON_HUMAN_VALUE {
					resourceContext.SetAliasUpdate(identityID, true)
				}
			}

		} else if contextProperty.PropertyName == IDENTITYNAME_PROPERTY_NAME {

			for _, value := range contextProperty.Include {
				if len(value) > 0 {
					resourceContext.SetOwnerEmailName(identityID, value)
				}
			}

		} else if contextProperty.PropertyName == EMAIL_PROPERTY_NAME {

			for _, value := range contextProperty.Include {
				if len(value) > 0 {
					email := []string{value}
					resourceContext.SetDerivedEmailInclusions(identityID, email)
				}
			}

			for _, value := range contextProperty.Exclude {
				if len(value) > 0 {
					email := []string{value}
					resourceContext.SetDerivedEmailExclusions(identityID, email)
				}
			}

		} else if contextProperty.PropertyName == PARTNER_EMAIL_PROPERTY_NAME {
			for _, value := range contextProperty.Include {
				if len(value) > 0 {
					resourceContext.SetChildPrimaryEmail(value, identityID)

					// adding a proxy record to incorporate demerging of other potential partner derivations
					resourceContext.SetChildPrimaryEmail(value+"<PROXY>", value)
				}
			}
		}
	}
}
