package context

import (
	"regexp"
	"strings"

	"github.com/precize/common"
)

func isTeamKey(tagKey string) bool {

	for r := range teamTagKeys {
		regex := regexp.MustCompile(r)
		if regex.MatchString(strings.ToLower(tagKey)) {
			return true
		}
	}

	return false
}

func GetTeamNameFromValue(str string) (team string) {

	var (
		matched    string
		matchedLen int
	)

	str = strings.ToLower(str)

	for teamName, values := range teamValues {
		for _, val := range values {
			if strings.Contains(val, `\b`) {
				// \b whole word match does not consider _ as special character
				str = strings.ReplaceAll(str, "_", "-")
			}

			regex1 := regexp.MustCompile(val)
			if regex1.MatchString(str) {

				isTeam := true

				for _, notVal := range notMatchValues[val] {
					regex2 := regexp.MustCompile(notVal)
					if regex2.MatchString(str) {
						isTeam = false
					}
				}

				if isTeam && len(teamName) > matchedLen {
					matchedLen = len(teamName)
					matched = teamName
				}
			}
		}
	}

	if len(matched) > 0 {
		team = matched
	}

	return
}

func GetTeamNameListFromValue(str string) (teamNames []string) {

	str = strings.ToLower(str)

	for teamName, values := range teamValues {
		for _, val := range values {
			if strings.Contains(val, `\b`) {
				// \b whole word match does not consider _ as special character
				str = strings.ReplaceAll(str, "_", "-")
			}

			regex1 := regexp.MustCompile(val)
			if regex1.MatchString(str) {

				isTeam := true

				for _, notVal := range notMatchValues[val] {
					regex2 := regexp.MustCompile(notVal)
					if regex2.MatchString(str) {
						isTeam = false
					}
				}

				if isTeam {
					teamNames = append(teamNames, teamName)
				}
			}
		}
	}

	return
}

func getInheritedTeam(parentDoc common.ResourceContextInsertDoc) (inheritedTeam []common.ResourceContextItem) {

	if len(parentDoc.DefinedTeam) > 0 {
		inheritedTeam = append(inheritedTeam, parentDoc.DefinedTeam...)
	} else if len(parentDoc.DerivedTeam) > 0 {
		inheritedTeam = append(inheritedTeam, parentDoc.DerivedTeam...)
	} else if len(parentDoc.InheritedTeam) > 0 {
		inheritedTeam = append(inheritedTeam, parentDoc.InheritedTeam...)
	}

	return
}

func GetUniqueTeamContext(resourceContextDoc *common.ResourceContextInsertDoc) (team []string) {

	uniqueTeam := make(map[string]struct{})

	getUniqueContext(&resourceContextDoc.DefinedTeam, uniqueTeam)
	getUniqueContext(&resourceContextDoc.DerivedTeam, uniqueTeam)
	getUniqueContext(&resourceContextDoc.InheritedTeam, uniqueTeam)

	for teamName := range uniqueTeam {
		team = append(team, teamName)
	}

	return
}

func AddTeamToGlobalTeams(team, tenantID string) {

	globalValuesMutex.Lock()

	var globallyExists bool

	for _, defaultTeamValue := range defaultTeamValues {
		subTeams := strings.Split(defaultTeamValue, ",")
		for _, subTeam := range subTeams {
			if strings.ToLower(subTeam) == strings.ToLower(team) {
				globallyExists = true
				break
			}
		}

		if globallyExists {
			break
		}
	}

	if !globallyExists {
		// before adding the team into defaultTeamValues check if it is an actual team
		if common.VerifyTeamName(team, tenantID, aiRejectedTeamValues) {
			defaultTeamValues = append(defaultTeamValues, team)
		}
	}

	globalValuesMutex.Unlock()
}

func processResourceNamesForTeams(resourceContext *ResourceContext, contextDocIDs []string) {

	var resourceNames = make([]string, 0, len(contextDocIDs))

	for _, contextDocID := range contextDocIDs {
		if contextDoc, ok := resourceContext.GetResourceContextInsertDoc(contextDocID); ok {
			if len(contextDoc.ResourceName) > 0 {
				resourceNames = append(resourceNames, strings.ToLower(contextDoc.ResourceName))
			}
		}
	}

	teams := common.TeamNamesFromList(resourceNames, resourceContext.TenantID)

	for _, team := range teams {
		team = FormatContextValue(team)
		AddTeamToGlobalTeams(team, resourceContext.TenantID)
	}
}
