package context

import (
	"regexp"
	"strings"
	"sync"

	"github.com/precize/common"
)

const (
	RAW_DATA       = "Raw Data"
	DATASET_COLUMN = "Dataset Column"
)

func GetComplianceNameFromValue(str string) string {

	str = strings.ToLower(str)

	for compliance, value := range complianceKeyOrValues {

		if strings.Contains(value, `\b`) {
			// \b whole word match does not consider _ as special character
			str = strings.ReplaceAll(str, "_", "-")
		}

		regex := regexp.MustCompile(value)
		if regex.MatchString(str) {
			return compliance
		}
	}

	return ""
}

func GetComplianceNameListFromValue(str string) (complianceNames []string) {

	str = strings.ToLower(str)

	for compliance, value := range complianceKeyOrValues {

		if strings.Contains(value, `\b`) {
			// \b whole word match does not consider _ as special character
			str = strings.ReplaceAll(str, "_", "-")
		}

		regex := regexp.MustCompile(value)
		if regex.MatchString(str) {
			complianceNames = append(complianceNames, compliance)
		}
	}

	return
}

func getInheritedCompliance(parentDoc common.ResourceContextInsertDoc) (inheritedCompliance []common.ResourceContextItem) {

	if len(parentDoc.DefinedCompliance) > 0 {
		inheritedCompliance = append(inheritedCompliance, parentDoc.DefinedCompliance...)
	} else if len(parentDoc.InheritedCompliance) > 0 {
		inheritedCompliance = append(inheritedCompliance, parentDoc.InheritedCompliance...)
	}

	return
}

func GetUniqueComplianceContext(resourceContextDoc *common.ResourceContextInsertDoc, accountCompliance *sync.Map) (compliance []string) {

	uniqueCompliance := make(map[string]struct{})

	getUniqueContext(&resourceContextDoc.DefinedCompliance, uniqueCompliance)
	getUniqueContext(&resourceContextDoc.DerivedCompliance, uniqueCompliance)

	if len(uniqueCompliance) > 0 {
		switch resourceContextDoc.ResourceType {
		case common.AZURE_SUBSCRIPTION_RESOURCE_TYPE, common.AWS_ACCOUNT_RESOURCE_TYPE,
			common.AWS_ORG_RESOURCE_TYPE, common.GCP_ORG_RESOURCE_TYPE, common.AZURE_RG_RESOURCE_TYPE,
			common.GCP_FOLDER_RESOURCE_TYPE, common.GCP_PROJECT_RESOURCE_TYPE, common.AZURE_TENANT_RESOURCE_TYPE, common.AZURE_MGMTGRP_RESOURCE_TYPE, common.AWS_ORGUNIT_RESOURCE_TYPE:

		default:
			var parentContextID string
			var parentComplianceMap map[string]struct{}

			switch resourceContextDoc.ServiceID {
			case common.AWS_SERVICE_ID_INT:
				parentContextID = common.GenerateCombinedHashID(resourceContextDoc.Account, common.AWS_ACCOUNT_RESOURCE_TYPE, resourceContextDoc.Account, resourceContextDoc.LastCollectedAt, resourceContextDoc.TenantID)
			case common.AZURE_SERVICE_ID_INT:
				parentContextID = common.GenerateCombinedHashID(resourceContextDoc.Account, common.AZURE_RG_RESOURCE_TYPE, resourceContextDoc.Account, resourceContextDoc.LastCollectedAt, resourceContextDoc.TenantID)
			case common.GCP_SERVICE_ID_INT:
				parentContextID = common.GenerateCombinedHashID(resourceContextDoc.Account, common.GCP_PROJECT_RESOURCE_TYPE, resourceContextDoc.Account, resourceContextDoc.LastCollectedAt, resourceContextDoc.TenantID)
			}

			if parentContextID != "" {
				if existingVal, loaded := accountCompliance.LoadOrStore(parentContextID, uniqueCompliance); loaded {

					if existingMap, ok := existingVal.(map[string]struct{}); ok {
						parentComplianceMap = existingMap
						for k := range uniqueCompliance {
							parentComplianceMap[k] = struct{}{}
						}
						accountCompliance.Store(parentContextID, parentComplianceMap)
					}
				}
			}
		}
	}

	getUniqueContext(&resourceContextDoc.InheritedCompliance, uniqueCompliance)

	for complianceName := range uniqueCompliance {
		compliance = append(compliance, complianceName)
	}

	return
}
