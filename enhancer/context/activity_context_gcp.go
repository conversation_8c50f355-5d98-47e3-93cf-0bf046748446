package context

import (
	"encoding/json"
	"fmt"
	"regexp"
	"strings"

	"github.com/precize/common"
	"github.com/precize/logger"
)

func isServiceAccountEmail(email string) bool {
	return strings.HasSuffix(email, ".gserviceaccount.com")
}

type GCPLog struct {
	ProtoPayload ProtoPayload `json:"protoPayload"`
}

type ProtoPayload struct {
	Type               string             `json:"@type"`
	AuthenticationInfo AuthenticationInfo `json:"authenticationInfo"`
}

type AuthenticationInfo struct {
	PrincipalEmail               string                     `json:"principalEmail"`
	ServiceAccountKeyName        string                     `json:"serviceAccountKeyName"`
	ServiceAccountDelegationInfo []ServiceAccountDelegation `json:"serviceAccountDelegationInfo"`
	ServiceDelegationHistory     ServiceDelegationHistory   `json:"serviceDelegationHistory"`
}

type ServiceAccountDelegation struct {
	FirstPartyPrincipal FirstPartyPrincipal `json:"firstPartyPrincipal"`
}

type FirstPartyPrincipal struct {
	PrincipalEmail string `json:"principalEmail"`
}

type ServiceDelegationHistory struct {
	OriginalPrincipal string `json:"originalPrincipal"`
}

func parseGCPActivity(resourceContext *ResourceContext, eventInfo EventInfo,
	activityUsers map[string]common.ResourceContextItem, resourceContextInsertDoc common.ResourceContextInsertDoc) {

	var gcpLog GCPLog

	if err := json.Unmarshal([]byte(eventInfo.eventJSONString), &gcpLog); err != nil {
		logger.Print(logger.ERROR, "Got error unmarshalling", []string{resourceContext.TenantID}, err)
		return
	}

	var serviceAccount = eventInfo.username

	_, serviceAccountDesc := parseServiceAccountData(serviceAccount, eventInfo.account, resourceContext)

	uniqueIdentities := make(map[string]struct{})

	// Using serviceAccountKey
	if serviceAccountKey := gcpLog.ProtoPayload.AuthenticationInfo.ServiceAccountKeyName; len(serviceAccountKey) > 0 {
		serviceAccountKey = strings.TrimPrefix(strings.ToLower(serviceAccountKey), "//iam.googleapis.com/")
		getServiceAccountKeyOwners(resourceContext, serviceAccountKey, eventInfo, activityUsers, resourceContextInsertDoc)
	}

	// Impersonating the service account (an identity with serviceAccountTokenCreator permission)
	serviceAccountDelegationInfo := gcpLog.ProtoPayload.AuthenticationInfo.ServiceAccountDelegationInfo
	for i, delegation := range serviceAccountDelegationInfo {
		if principalEmail := delegation.FirstPartyPrincipal.PrincipalEmail; len(principalEmail) > 0 {
			if isServiceAccountEmail(principalEmail) || strings.Contains(principalEmail, "serviceAccount:") {
				emailSplit := strings.Split(principalEmail, "serviceAccount:")
				if len(emailSplit) > 1 {
					principalEmail = emailSplit[1]
					if !isServiceAccountEmail(principalEmail) {
						continue
					}
				}

				getServiceAccountOwnersForActivity(resourceContext, principalEmail, eventInfo, activityUsers, uniqueIdentities, "User owned service account "+principalEmail+" has performed activities on the resource", "b", resourceContextInsertDoc)

				serviceAccountName := getGCPServiceAccountName(resourceContext, principalEmail, eventInfo.account)
				activityUsers[eventInfo.eventTime+"b"+fmt.Sprintf("%03d", len(serviceAccountDelegationInfo)-i)] = resourceContext.GetUserContextItem(serviceAccountName+SERVICEACCOUNT_USER_SUFFIX, common.ACTIVITY_USER_TYPE, "Service account has performed activities on the resource by impersonating another Service Account "+serviceAccount, principalEmail, resourceContextInsertDoc.Account, &common.ResourceCtxEvent{
					Name:   eventInfo.event,
					Region: eventInfo.region,
					Time:   eventInfo.eventTime,
				})
			} else {
				emailSplit := strings.Split(principalEmail, "user:")
				if len(emailSplit) > 1 {
					emailSplit = strings.Split(emailSplit[1], "?uid")
					principalEmail = emailSplit[0]
				}

				activityUsers[eventInfo.eventTime+"b"+fmt.Sprintf("%03d", len(serviceAccountDelegationInfo)-i)] = resourceContext.GetUserContextItem(principalEmail, common.ACTIVITY_USER_TYPE, "User has performed activities on the resource by impersonating the Service Account "+serviceAccount, "", resourceContextInsertDoc.Account, &common.ResourceCtxEvent{
					Name:   eventInfo.event,
					Region: eventInfo.region,
					Time:   eventInfo.eventTime,
				})
			}
		}
	}

	// Service agent acting on behalf of another identity
	if originalPrincipal := gcpLog.ProtoPayload.AuthenticationInfo.ServiceDelegationHistory.OriginalPrincipal; len(originalPrincipal) > 0 {
		if strings.Contains(originalPrincipal, "serviceAccount:") {
			emailSplit := strings.Split(originalPrincipal, "serviceAccount:")
			if len(emailSplit) > 1 {
				email := emailSplit[1]
				if isServiceAccountEmail(email) {
					getServiceAccountOwnersForActivity(resourceContext, email, eventInfo, activityUsers, uniqueIdentities, "User owned service account "+email+" has performed activities on the resource", "b", resourceContextInsertDoc)

					serviceAccountName := getGCPServiceAccountName(resourceContext, email, eventInfo.account)
					activityUsers[eventInfo.eventTime+"b"] = resourceContext.GetUserContextItem(serviceAccountName+SERVICEACCOUNT_USER_SUFFIX, common.ACTIVITY_USER_TYPE, "Service account has performed activities on the resource by delegating tasks to another Service Account "+serviceAccount, email, resourceContextInsertDoc.Account, &common.ResourceCtxEvent{
						Name:   eventInfo.event,
						Region: eventInfo.region,
						Time:   eventInfo.eventTime,
					})
				}
			}
		} else if strings.Contains(originalPrincipal, "user:") {
			emailSplit := strings.Split(originalPrincipal, "user:")
			if len(emailSplit) > 1 {
				emailSplit = strings.Split(emailSplit[1], "?uid")
				email := emailSplit[0]
				activityUsers[eventInfo.eventTime+"b"] = resourceContext.GetUserContextItem(email, common.ACTIVITY_USER_TYPE,
					"User has performed activities on the resource by delegating tasks to the Service Account "+serviceAccount, "", resourceContextInsertDoc.Account, &common.ResourceCtxEvent{
						Name:   eventInfo.event,
						Region: eventInfo.region,
						Time:   eventInfo.eventTime,
					})
			}
		}
	}

	// Add Service Account owner as an owner of this resource via Activity
	getServiceAccountOwnersForActivity(resourceContext, serviceAccount, eventInfo, activityUsers, uniqueIdentities,
		"User owned service account "+serviceAccount+" has performed activities on the resource", "a", resourceContextInsertDoc)

	serviceAccountName := getGCPServiceAccountName(resourceContext, serviceAccount, eventInfo.account)

	// Add Service Account as an owner of this resource via Activity
	activityUsers[eventInfo.eventTime+"a"] = resourceContext.GetUserContextItem(serviceAccountName+SERVICEACCOUNT_USER_SUFFIX, common.ACTIVITY_USER_TYPE,
		serviceAccountDesc, serviceAccount, resourceContextInsertDoc.Account, &common.ResourceCtxEvent{
			Name:   eventInfo.event,
			Region: eventInfo.region,
			Time:   eventInfo.eventTime,
		})
}

func getServiceAccountOwnersForActivity(resourceContext *ResourceContext, serviceAccount string, eventInfo EventInfo,
	activityUsers map[string]common.ResourceContextItem, uniqueIdentities map[string]struct{}, desc, sortAlphabet string, resourceContextInsertDoc common.ResourceContextInsertDoc) {

	if _, ok := uniqueIdentities[serviceAccount]; ok {
		return
	}

	uniqueIdentities[serviceAccount] = struct{}{}
	depth := len(sortAlphabet)

	saDoc := getServiceAccountDoc(resourceContext, serviceAccount, eventInfo.account)

	// Resourcename user will not be derived for the service identity at this time. Additional entry for it if exists
	serviceAccountName := serviceAccount
	if len(saDoc.ResourceName) > 0 {
		serviceAccountName = saDoc.ResourceName
	}

	activityUsers[eventInfo.eventTime+sortAlphabet+"c"] = resourceContext.GetUserContextItem(serviceAccountName+PLACEHOLDER_USER_SUFFIX, common.ACTIVITY_USER_TYPE,
		desc, "", resourceContextInsertDoc.Account, &common.ResourceCtxEvent{
			Name:          eventInfo.event,
			Region:        eventInfo.region,
			Time:          eventInfo.eventTime,
			IndirectEvent: true,
		})

	for i, defined := range saDoc.DefinedOwners {
		if strings.HasSuffix(defined.Name, SERVICEACCOUNT_USER_SUFFIX) {
			if len(defined.IdentityId) > 0 && depth <= 5 {
				// Recursive call
				getServiceAccountOwnersForActivity(resourceContext, defined.IdentityId, eventInfo, activityUsers, uniqueIdentities, getIdentityChainDescription(desc, defined.IdentityId, depth), sortAlphabet+sortAlphabet, resourceContextInsertDoc)
			}
		} else if IsNonHumanEmail(defined.Name, resourceContext) {
			uniqueGroupEmailIdentities := make(map[string]struct{})
			getNonHumanEmailOwnersForActivity(resourceContext, defined.Name, eventInfo, activityUsers, uniqueGroupEmailIdentities, "User owned group email "+defined.Name+" has performed activities on the resource", sortAlphabet+sortAlphabet)
		} else {
			if _, ok := uniqueIdentities[defined.Name]; !ok || depth == 1 {
				uniqueIdentities[defined.Name] = struct{}{}
				activityUsers[eventInfo.eventTime+sortAlphabet+"b"+fmt.Sprintf("%03d", len(saDoc.DefinedOwners)-i)] = resourceContext.GetUserContextItem(defined.Name, common.ACTIVITY_USER_TYPE,
					desc, "", resourceContextInsertDoc.Account, &common.ResourceCtxEvent{
						Name:          eventInfo.event,
						Region:        eventInfo.region,
						Time:          eventInfo.eventTime,
						IndirectEvent: true,
					})
			}
		}
	}

	for i, derived := range saDoc.DerivedOwners {
		if strings.HasSuffix(derived.Name, SERVICEACCOUNT_USER_SUFFIX) {
			if len(derived.IdentityId) > 0 && depth <= 5 {
				// Recursive call
				getServiceAccountOwnersForActivity(resourceContext, derived.IdentityId, eventInfo, activityUsers, uniqueIdentities, getIdentityChainDescription(desc, derived.IdentityId, depth), sortAlphabet+sortAlphabet, resourceContextInsertDoc)
			}
		} else if IsNonHumanEmail(derived.Name, resourceContext) {
			uniqueGroupEmailIdentities := make(map[string]struct{})
			getNonHumanEmailOwnersForActivity(resourceContext, derived.Name, eventInfo, activityUsers, uniqueGroupEmailIdentities, "User owned group email "+derived.Name+" has performed activities on the resource", sortAlphabet+sortAlphabet)
		} else {
			if _, ok := uniqueIdentities[derived.Name]; !ok || depth == 1 {
				uniqueIdentities[derived.Name] = struct{}{}
				activityUsers[eventInfo.eventTime+sortAlphabet+"a"+fmt.Sprintf("%03d", len(saDoc.DerivedOwners)-i)] = resourceContext.GetUserContextItem(derived.Name, common.ACTIVITY_USER_TYPE,
					desc, "", resourceContextInsertDoc.Account, &common.ResourceCtxEvent{
						Name:          eventInfo.event,
						Region:        eventInfo.region,
						Time:          eventInfo.eventTime,
						IndirectEvent: true,
					})
			}
		}
	}
}

func getServiceAccountKeyOwners(resourceContext *ResourceContext, serviceAccountKey string, eventInfo EventInfo,
	activityUsers map[string]common.ResourceContextItem, resourceContextInsertDoc common.ResourceContextInsertDoc) {

	serviceAccountKeyContextID := common.GenerateCombinedHashID(serviceAccountKey, common.GCP_SERVICEACCOUNTKEY_RESOURCE_TYPE, eventInfo.account, resourceContext.LastCollectedAt, resourceContext.TenantID)

	if servAccRscInsertDoc, ok := resourceContext.GetResourceContextInsertDoc(serviceAccountKeyContextID); ok {
		for i, defined := range servAccRscInsertDoc.DefinedOwners {
			if strings.HasSuffix(defined.Name, SERVICEACCOUNT_USER_SUFFIX) {
				uniqueIdentities := make(map[string]struct{})
				if len(defined.IdentityId) > 0 {
					getServiceAccountOwnersForActivity(resourceContext, defined.IdentityId, eventInfo, activityUsers, uniqueIdentities, "User owned service account key "+serviceAccountKey+" has performed activities on the resource", "c", resourceContextInsertDoc)
				}
			} else {
				activityUsers[eventInfo.eventTime+"c"+fmt.Sprintf("%03d", len(servAccRscInsertDoc.DefinedOwners)-i)] = resourceContext.GetUserContextItem(defined.Name, common.ACTIVITY_USER_TYPE,
					"User owned Service Account Key "+serviceAccountKey+" has been used to do activities on the resource", "", resourceContextInsertDoc.Account, &common.ResourceCtxEvent{
						Name:          eventInfo.event,
						Region:        eventInfo.region,
						Time:          eventInfo.eventTime,
						IndirectEvent: true,
					})
			}
		}

		for i, derived := range servAccRscInsertDoc.DerivedOwners {
			if strings.HasSuffix(derived.Name, SERVICEACCOUNT_USER_SUFFIX) {
				uniqueIdentities := make(map[string]struct{})
				if len(derived.IdentityId) > 0 {
					getServiceAccountOwnersForActivity(resourceContext, derived.IdentityId, eventInfo, activityUsers, uniqueIdentities, "User owned service account key "+serviceAccountKey+" has performed activities on the resource", "b", resourceContextInsertDoc)
				}
			} else {
				activityUsers[eventInfo.eventTime+"b"+fmt.Sprintf("%03d", len(servAccRscInsertDoc.DerivedOwners)-i)] = resourceContext.GetUserContextItem(derived.Name, common.ACTIVITY_USER_TYPE,
					"User owned Service Account Key "+serviceAccountKey+" has been used to do activities on the resource", "", resourceContextInsertDoc.Account, &common.ResourceCtxEvent{
						Name:          eventInfo.event,
						Region:        eventInfo.region,
						Time:          eventInfo.eventTime,
						IndirectEvent: true,
					})
			}
		}
	}
}

func getServiceAccountOwnersForPolicy(resourceContext *ResourceContext, serviceAccount, defaultProject string,
	resourceContextInsertDoc *common.ResourceContextInsertDoc, uniqueIdentities map[string]struct{}, desc string, depth int) {

	if _, ok := uniqueIdentities[serviceAccount]; ok {
		return
	}

	uniqueIdentities[serviceAccount] = struct{}{}

	saDoc := getServiceAccountDoc(resourceContext, serviceAccount, defaultProject)

	// Resourcename user will not be derived for the service identity at this time. Additional entry for it if exists
	serviceAccountName := serviceAccount
	if len(saDoc.ResourceName) > 0 {
		serviceAccountName = saDoc.ResourceName
	}

	resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners = append(
		resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners, resourceContext.GetUserContextItem(serviceAccountName+PLACEHOLDER_USER_SUFFIX, common.POLICYBINDING_USER_TYPE,
			desc, "", resourceContextInsertDoc.Account, nil))

	for _, defined := range saDoc.DefinedOwners {
		if strings.HasSuffix(defined.Name, SERVICEACCOUNT_USER_SUFFIX) {
			if len(defined.IdentityId) > 0 && depth <= 5 {
				// Recursive call
				getServiceAccountOwnersForPolicy(resourceContext, defined.IdentityId, defaultProject, resourceContextInsertDoc, uniqueIdentities, getIdentityChainDescription(desc, defined.IdentityId, depth), depth+1)
			}
		} else if IsNonHumanEmail(defined.Name, resourceContext) {
			uniqueGroupEmailIdentities := make(map[string]struct{})
			getNonHumanEmailOwners(resourceContext, defined.Name, resourceContextInsertDoc, uniqueGroupEmailIdentities, "User owned group email "+defined.Name+" has been assigned policy binding for the resource", depth+1)
		} else {
			if _, ok := uniqueIdentities[defined.Name]; !ok || depth == 1 {
				uniqueIdentities[defined.Name] = struct{}{}
				resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners = append(
					resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners, resourceContext.GetUserContextItem(defined.Name, common.POLICYBINDING_USER_TYPE,
						desc, "", resourceContextInsertDoc.Account, nil))
			}
		}
	}

	for _, derived := range saDoc.DerivedOwners {
		if strings.HasSuffix(derived.Name, SERVICEACCOUNT_USER_SUFFIX) {
			if len(derived.IdentityId) > 0 && depth <= 5 {
				// Recursive call
				getServiceAccountOwnersForPolicy(resourceContext, derived.IdentityId, defaultProject, resourceContextInsertDoc, uniqueIdentities, getIdentityChainDescription(desc, derived.IdentityId, depth), depth+1)
			}
		} else if IsNonHumanEmail(derived.Name, resourceContext) {
			uniqueGroupEmailIdentities := make(map[string]struct{})
			getNonHumanEmailOwners(resourceContext, derived.Name, resourceContextInsertDoc, uniqueGroupEmailIdentities, "User owned group email "+derived.Name+" has been assigned policy binding for the resource", depth+1)
		} else {
			if _, ok := uniqueIdentities[derived.Name]; !ok || depth == 1 {
				uniqueIdentities[derived.Name] = struct{}{}
				resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners = append(
					resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners, resourceContext.GetUserContextItem(derived.Name, common.POLICYBINDING_USER_TYPE,
						desc, "", resourceContextInsertDoc.Account, nil))
			}
		}
	}
}

func parseServiceAccountData(serviceAccount, defaultProject string, resourceContext *ResourceContext) (projectID, serviceAccountDesc string) {

	switch serviceAccount {
	case "<EMAIL>":
		projectID = defaultProject
		serviceAccountDesc = "Google Service Account for Apps Scripts has performed activities on the resource"
		return
	case "<EMAIL>":
		projectID = defaultProject
		serviceAccountDesc = "Service Agent Manager Service Account has performed activities on the resource"
		return
	}

	for saType, pattern := range map[string]string{
		"appspot":       `^[a-z0-9-]+@appspot\.gserviceaccount\.com$`,             // <project-id>@appspot.gserviceaccount.com
		"compute":       `^[0-9]+-compute@developer\.gserviceaccount\.com$`,       // <project-number>-<EMAIL>
		"cloudservices": `^[0-9]+@cloudservices\.gserviceaccount\.com$`,           // <project-number>@cloudservices.gserviceaccount.com
		"cloudbuild":    `^[0-9]+@cloudbuild\.gserviceaccount\.com$`,              // <project-number>@cloudbuild.gserviceaccount.com
		"custom":        `^[a-zA-Z0-9-_]+@[a-z0-9-]+\.iam\.gserviceaccount\.com$`, // <service-account-name>@<project-id>.iam.gserviceaccount.com
		"system":        `^[a-zA-Z0-9-_]+@system\.gserviceaccount\.com$`,          // <system-term>@system.gserviceaccount.com
	} {
		re := regexp.MustCompile(pattern)
		if re.MatchString(serviceAccount) {
			switch saType {
			// FYI - projectName here is actually gcp projectId and project-id is actually gcp project-number
			case "appspot":
				projectName := strings.TrimSuffix(serviceAccount, "@appspot.gserviceaccount.com")
				projectID, _ = resourceContext.GetGCPProjectIDToNumber(projectName)
				serviceAccountDesc = "App Engine managed Service Account from project " + projectName + " has performed activities on the resource"
			case "compute":
				projectID = strings.TrimSuffix(serviceAccount, "-<EMAIL>")
				projectName, _ := resourceContext.GetGCPProjectNumberToID(projectID)
				serviceAccountDesc = "Compute Engine managed Service Account from project " + projectName + " has performed activities on the resource"
			case "cloudservices":
				projectID = strings.TrimSuffix(serviceAccount, "@cloudservices.gserviceaccount.com")
				projectName, _ := resourceContext.GetGCPProjectNumberToID(projectID)
				serviceAccountDesc = "Cloud API Services managed Service Account from project " + projectName + " has performed activities on the resource"
			case "cloudbuild":
				projectID = strings.TrimSuffix(serviceAccount, "@cloudbuild.gserviceaccount.com")
				projectName, _ := resourceContext.GetGCPProjectNumberToID(projectID)
				serviceAccountDesc = "Cloud Build managed Service Account from project " + projectName + " has performed activities on the resource"
			case "custom":
				split := strings.Split(serviceAccount, "@")
				if len(split) > 1 {
					tmpProjectName := strings.TrimSuffix(split[1], ".iam.gserviceaccount.com")
					if tmpProjectID, ok := resourceContext.GetGCPProjectIDToNumber(tmpProjectName); ok {
						projectID = tmpProjectID
						serviceAccountDesc = "User-managed Service Account from internal project " + tmpProjectName + " has performed activities on the resource"
					} else if serviceAgentKeyword(tmpProjectName) {
						// Service agent (has multiple formats)
						projectID = defaultProject
						projectName, _ := resourceContext.GetGCPProjectNumberToID(defaultProject)
						serviceAccountDesc = "Service managed Service Account (Service Agent) from project " + projectName + " has performed activities on the resource"
					} else {
						projectID = defaultProject
						serviceAccountDesc = "User-managed Service Account from external project " + tmpProjectName + " has performed activities on the resource"
					}
				}
			case "system":
				projectID = defaultProject
				serviceAccountDesc = "System Service Account has performed activities on the resource"
			default:
				logger.Print(logger.INFO, "Unhandled GCP Service Account Parse", serviceAccount)
				projectID = defaultProject
				serviceAccountDesc = "Service Account has performed activities on the resource"
			}
		}
	}

	return
}

func serviceAgentKeyword(str string) bool {

	switch str {
	case "bigquery-encryption", "cloud-filer", "cloud-memcache-sa", "cloud-ml.google.com", "cloud-redis", "cloud-tpu", "cloudcomposer-accounts", "compute-system", "container-analysis", "container-engine-robot", "containerregistry", "dataflow-service-producer-prod", "dataproc-accounts", "dlp-api", "firebase-rules", "gae-api-prod.google.com", "gcf-admin-robot",
		"gcp-gae-service", "gcp-ri-aiplatform", "gcp-ri-contactcenterinsights", "gcp-ri-identitypool", "gcp-sa-accessapproval", "gcp-sa-adsdatahub", "gcp-sa-aiplatform", "gcp-sa-aiplatform-cc", "gcp-sa-aiplatform-ft", "gcp-sa-aiplatform-is", "gcp-sa-aiplatform-re", "gcp-sa-aiplatform-vm", "gcp-sa-alloydb", "gcp-sa-anthos", "gcp-sa-anthosaudit", "gcp-sa-anthosconfigmanagement",
		"gcp-sa-anthosidentityservice", "gcp-sa-anthospolicycontroller", "gcp-sa-anthossupport", "gcp-sa-apigateway", "gcp-sa-apigateway-mgmt", "gcp-sa-apigee", "gcp-sa-apigeeregistry", "gcp-sa-apihub", "gcp-sa-apikeys", "gcp-sa-apim", "gcp-sa-appdevexperience", "gcp-sa-apphub", "gcp-sa-artifactregistry", "gcp-sa-asm-hpsa", "gcp-sa-assuredoss", "gcp-sa-assuredworkloads",
		"gcp-sa-audit-manager", "gcp-sa-automl", "gcp-sa-backupdr", "gcp-sa-backupdr-pr", "gcp-sa-backupdr-run", "gcp-sa-bigquery-condel", "gcp-sa-bigquery-consp", "gcp-sa-bigqueryconnection", "gcp-sa-bigquerydatatransfer", "gcp-sa-bigqueryri", "gcp-sa-bigqueryspark", "gcp-sa-bigquerytardis", "gcp-sa-bigtable", "gcp-sa-binaryauthorization", "gcp-sa-bms", "gcp-sa-bne",
		"gcp-sa-bundles", "gcp-sa-ccai-cmek", "gcp-sa-ccaip", "gcp-sa-ccinsights-cmek", "gcp-sa-certificatemanager", "gcp-sa-chronicle", "gcp-sa-chronicle-soar", "gcp-sa-chronicle-sv", "gcp-sa-cloud-cw", "gcp-sa-cloud-cw-cmek", "gcp-sa-cloud-ekg", "gcp-sa-cloud-sql", "gcp-sa-cloud-trace", "gcp-sa-cloudaicompanion", "gcp-sa-cloudasset", "gcp-sa-cloudbatch", "gcp-sa-cloudbuild",
		"gcp-sa-cloudcontrolspartner", "gcp-sa-clouddeploy", "gcp-sa-cloudkms", "gcp-sa-cloudoptim", "gcp-sa-cloudresourcemanager", "gcp-sa-cloudscheduler", "gcp-sa-cloudtasks", "gcp-sa-compute-usage", "gcp-sa-config", "gcp-sa-configdelivery", "gcp-sa-connectors", "gcp-sa-contactcenterinsights", "gcp-sa-containerscanning", "gcp-sa-containersec", "gcp-sa-crashlytics",
		"gcp-sa-dataconnectors", "gcp-sa-dataform", "gcp-sa-datafusion", "gcp-sa-datalabeling", "gcp-sa-datamigration", "gcp-sa-datapipelines", "gcp-sa-dataplex", "gcp-sa-dataplex-cmek", "gcp-sa-dataprocrmnode", "gcp-sa-datastream", "gcp-sa-datastudio", "gcp-sa-dep", "gcp-sa-designcenter", "gcp-sa-devconnect", "gcp-sa-dialogflow", "gcp-sa-dialogflow-cmek", "gcp-sa-discoveryengine",
		"gcp-sa-dns", "gcp-sa-edgecontainer", "gcp-sa-edgecontainercluster", "gcp-sa-edgecontainergcr", "gcp-sa-effectivepolicy", "gcp-sa-ekms", "gcp-sa-endpoints", "gcp-sa-eventarc", "gcp-sa-firebase", "gcp-sa-firebaseappcheck", "gcp-sa-firebaseapphosting", "gcp-sa-firebasedatabase", "gcp-sa-firebasedataconnect", "gcp-sa-firebaseml", "gcp-sa-firebasemods", "gcp-sa-firebasestorage",
		"gcp-sa-firebasevertexai", "gcp-sa-firestore", "gcp-sa-firewallinsights", "gcp-sa-fs-spanner", "gcp-sa-gkebackup", "gcp-sa-gkedataplanev2", "gcp-sa-gkehub", "gcp-sa-gkemulticloud", "gcp-sa-gkemulticloudcontainer", "gcp-sa-gkemulticloudcpmachine", "gcp-sa-gkemulticloudnpmachine", "gcp-sa-gkenode", "gcp-sa-gkeonprem", "gcp-sa-gsuiteaddons", "gcp-sa-healthcare", "gcp-sa-iap",
		"gcp-sa-identitytoolkit", "gcp-sa-integrations", "gcp-sa-issuerswitch", "gcp-sa-ivs", "gcp-sa-krmapihosting", "gcp-sa-krmapihosting-dataplane", "gcp-sa-ktd-control", "gcp-sa-ktd-hpsa", "gcp-sa-lifesciences", "gcp-sa-livestream", "gcp-sa-logging", "gcp-sa-looker", "gcp-sa-managedflink", "gcp-sa-managedkafka", "gcp-sa-mcmetering", "gcp-sa-mcsd", "gcp-sa-memorystore",
		"gcp-sa-meshconfig", "gcp-sa-meshcontrolplane", "gcp-sa-meshdataplane", "gcp-sa-metastore", "gcp-sa-mi", "gcp-sa-migcenter", "gcp-sa-modelarmor", "gcp-sa-monitoring", "gcp-sa-monitoring-notification", "gcp-sa-multiclusteringress", "gcp-sa-netapp", "gcp-sa-networkactions", "gcp-sa-networkconnectivity", "gcp-sa-networkmanagement", "gcp-sa-networksecurity", "gcp-sa-notebooks",
		"gcp-sa-notebooks-vm", "gcp-sa-notebooksecurityscanner", "gcp-sa-nss-hpsa", "gcp-sa-oci", "gcp-sa-ondemandscanning", "gcp-sa-osconfig", "gcp-sa-osconfig-rollout", "gcp-sa-othercloudcfg", "gcp-sa-pam", "gcp-sa-parallelstore", "gcp-sa-playbooks", "gcp-sa-pm", "gcp-sa-privateca", "gcp-sa-prod-bigqueryomni", "gcp-sa-prod-dai-core", "gcp-sa-progrollout", "gcp-sa-pubsub",
		"gcp-sa-pubsublite", "gcp-sa-rbe", "gcp-sa-recommendationengine", "gcp-sa-remotebuild", "gcp-sa-retail", "gcp-sa-riskmanager", "gcp-sa-rma", "gcp-sa-routeoptim", "gcp-sa-runapps", "gcp-sa-scc-notification", "gcp-sa-scc-vmtd", "gcp-sa-sccspanner", "gcp-sa-secretmanager", "gcp-sa-securewebproxy", "gcp-sa-securitycenter", "gcp-sa-servicedirectory",
		"gcp-sa-servicemesh", "gcp-sa-sourcemanager", "gcp-sa-spanner", "gcp-sa-spectrumsas", "gcp-sa-speech", "gcp-sa-storageinsights", "gcp-sa-stream", "gcp-sa-tpu", "gcp-sa-transcoder", "gcp-sa-transferappliance", "gcp-sa-translation", "gcp-sa-v1-remediator", "gcp-sa-vertex-agent", "gcp-sa-vertex-bp", "gcp-sa-vertex-es", "gcp-sa-vertex-eval", "gcp-sa-vertex-ex",
		"gcp-sa-vertex-ex-cc", "gcp-sa-vertex-mm", "gcp-sa-vertex-nb", "gcp-sa-vertex-op", "gcp-sa-vertex-rag", "gcp-sa-vertex-shtune", "gcp-sa-vertex-tune", "gcp-sa-visionai", "gcp-sa-vmmigration", "gcp-sa-vmwareengine", "gcp-sa-vpcaccess", "gcp-sa-websecurityscanner", "gcp-sa-workflows", "gcp-sa-workloadmanager", "gcp-sa-workstations", "gcp-sa-workstationsvm",
		"gs-project-accounts", "remotebuildexecution", "security-center-api", "serverless-robot-prod", "service-consumer-management", "service-networking", "storage-transfer-service":
		return true
	}

	return false
}

func getGCPEventActor(eventJSONString string, username *string) (actorType string) {

	var gcpLog GCPLog

	if err := json.Unmarshal([]byte(eventJSONString), &gcpLog); err != nil {
		return
	}

	authInfo := gcpLog.ProtoPayload.AuthenticationInfo

	if len(authInfo.ServiceAccountDelegationInfo) > 0 {
		for _, delegation := range authInfo.ServiceAccountDelegationInfo {
			if principalEmail := delegation.FirstPartyPrincipal.PrincipalEmail; len(principalEmail) > 0 {
				if isServiceAccountEmail(principalEmail) {
					emailSplit := strings.Split(principalEmail, "serviceAccount:")
					if len(emailSplit) > 1 {
						principalEmail = emailSplit[1]
						if !isServiceAccountEmail(principalEmail) {
							continue
						} else {
							*username = principalEmail
							*username += SERVICEACCOUNT_USER_SUFFIX
							return "GCP Service Account (Impersonated) "
						}
					}
				} else {
					emailSplit := strings.Split(principalEmail, "user:")
					if len(emailSplit) > 1 {
						emailSplit = strings.Split(emailSplit[1], "?uid")
						principalEmail = emailSplit[0]
					}
					*username = principalEmail
					return "GCP User (Impersonated) "
				}
			}
		}
	}

	if originalPrincipal := authInfo.ServiceDelegationHistory.OriginalPrincipal; len(originalPrincipal) > 0 {
		if strings.Contains(originalPrincipal, "serviceAccount:") {
			emailSplit := strings.Split(originalPrincipal, "serviceAccount:")
			if len(emailSplit) > 1 {
				email := emailSplit[1]
				if isServiceAccountEmail(email) {
					*username = email
					*username += SERVICEACCOUNT_USER_SUFFIX
					return "GCP Service Account (Delegated) "
				}
			}
		}
		if strings.Contains(originalPrincipal, "user:") {
			emailSplit := strings.Split(originalPrincipal, "user:")
			if len(emailSplit) > 1 {
				emailSplit = strings.Split(emailSplit[1], "?uid")
				*username = emailSplit[0]
				return "GCP User (Delegated) "
			}
		}
	}

	if keyName := authInfo.ServiceAccountKeyName; len(keyName) > 0 {
		*username += SERVICEACCOUNT_USER_SUFFIX
		return "GCP Service Account (Key Used) "
	}

	if isServiceAccountEmail(*username) {
		*username += SERVICEACCOUNT_USER_SUFFIX
		return "GCP Service Account "
	}

	return ""
}
