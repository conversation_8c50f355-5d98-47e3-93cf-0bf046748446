package context

import (
	"regexp"
	"strings"

	"github.com/precize/common"
)

func GetUserAgentCategoriesFromValue(userAgent string) (categories []string) {
	origUserAgent := userAgent
	userAgent = strings.ToLower(userAgent)

	var lowPriorityUserAgents = map[string]bool{
		AWS_SERVICE_USER_AGENT: true,
	}

	matches := make(map[string]bool)
	for category, patterns := range userAgentKeyOrValues {
		for _, pattern := range patterns {
			regex := regexp.MustCompile(pattern)
			if regex.MatchString(userAgent) {
				matches[category] = true
				break
			}
		}
	}

	// for more than one user agents are detected ignore the low priority one's
	if len(matches) > 1 {
		for category := range matches {
			if !lowPriorityUserAgents[category] {
				categories = append(categories, category)
			}
		}
		if len(categories) > 0 {
			return categories
		}
	} else if len(matches) <= 0 {
		matches[origUserAgent] = true
	}

	for category := range matches {
		categories = append(categories, category)
	}

	return categories
}

func GetUniqueUserAgentContext(resourceContextDoc *common.ResourceContextInsertDoc) (agents []string) {

	uniqueUserAgent := make(map[string]struct{})

	for _, v := range resourceContextDoc.UserAgents {
		uniqueUserAgent[v] = struct{}{}
	}

	resourceContextDoc.UserAgents = make([]string, 0)

	for userAgent := range uniqueUserAgent {
		agents = append(agents, userAgent)
		resourceContextDoc.UserAgents = append(resourceContextDoc.UserAgents, userAgent)
	}

	return
}
