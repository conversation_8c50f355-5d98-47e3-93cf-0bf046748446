package openai

var staticModelMetaData = map[string]any{

	"davinci-003": map[string]any{
		"description":           "These models can understand and generate natural language or code but are not trained with instruction following. These models are made to be replacements for the original GPT-3 base models and use the legacy Completions API. Use GPT-3.5 or GPT-4. 16384 tokens.",
		"use_case":              "Natural language understanding , Code generation",
		"deprecation_date":      nil,
		"legacy_model":          true,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    "Up to Sep 2021",
		"bias_data":             "{} , ",
	},
	"davinci-002": map[string]any{
		"description":           "These models can understand and generate natural language or code but are not trained with instruction following. These models are made to be replacements for the original GPT-3 base models and use the legacy Completions API. Use GPT-3.5 or GPT-4. 16384 tokens.",
		"use_case":              "Natural language understanding , Code generation",
		"deprecation_date":      nil,
		"legacy_model":          true,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    "Up to Oct 2023",
		"bias_data":             "{} , ",
	},
	"gpt-4o-mini": map[string]any{
		"description":           "GPT-4o mini (“o” for “omni”) is the most advanced model in the small models category, and the cheapest model yet. It is a multimodal (accepting text or image inputs and outputting text), has higher intelligence than gpt-3.5-turbo but is just as fast. It is meant to be used for smaller tasks, including vision tasks.",
		"use_case":              "Customer Support, Content Creation",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    "Up to Oct 2023",
		"bias_data":             "{} , ",
	},
	"gpt-4o-mini-2024-07-18": map[string]any{
		"description":           "GPT-4o mini (“o” for “omni”) is the most advanced model in the small models category, and the cheapest model yet. It is a multimodal (accepting text or image inputs and outputting text), has higher intelligence than gpt-3.5-turbo but is just as fast. It is meant to be used for smaller tasks, including vision tasks.",
		"use_case":              "Customer Support, Content Creation",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    "Up to Oct 2023",
		"bias_data":             "{} , ",
	},
	"text-babbage-001": map[string]any{
		"description":           "Babbage and Text Babbage are legacy models and replaced by newer babbage-002 and newer Davinci-002 models. These models can understand and generate natural language or code but are not trained with instruction following. These models are made to be replacements for the original GPT-3 base models and use the legacy Completions API. Use GPT-3.5 or GPT-4.",
		"use_case":              "Natural language understanding , Code generation",
		"deprecation_date":      "July 6, 2023",
		"legacy_model":          false,
		"retirement_date":       "July 5, 2024",
		"suggested_replacement": "gpt-35-turbo-instruct",
		"training_till_date":    "Up to Sep 2021",
		"bias_data":             "{} , ",
	},
	"code-davinci-002": map[string]any{
		"description":           "Babbage and Text Babbage are legacy models and replaced by newer davinci-002, davinci-003 models. These models can understand and generate natural language or code but are not trained with instruction following. These models are made to be replacements for the original GPT-3 base models and use the legacy Completions API. Use GPT-3.5 or GPT-4.",
		"use_case":              "Natural language understanding , Code generation",
		"deprecation_date":      "July 6, 2023",
		"legacy_model":          false,
		"retirement_date":       "July 5, 2024",
		"suggested_replacement": "gpt-35-turbo-instruct",
		"training_till_date":    nil,
		"bias_data":             "{} , ",
	},
	"text-embedding-3-large": map[string]any{
		"description":           "A set of models that can convert text into a numerical form. Embeddings are a numerical representation of text that can be used to measure the relatedness between two pieces of text. Embeddings are useful for search, clustering, recommendations, anomaly detection, and classification tasks. Increased performance over other embedding models and uses 3,072 output dimension.",
		"use_case":              "Text embedding , Natural language processing",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    "Jan 2024",
		"bias_data":             "{} , ",
	},
	"gpt-35-turbo": map[string]any{
		"description":           "A fast, inexpensive model for simple tasks from OpenAI. GPT-3.5 Turbo models can understand and generate natural language or code and have been optimized for chat using the Chat Completions API. The latest GPT-3.5 Turbo model with higher accuracy at responding in requested formats and a fix for a bug which caused a text encoding issue for non-English language function calls. Returns a maximum of 4,096 output tokens. Currently points to gpt-3.5-turbo-0125. 16,385 tokens.",
		"use_case":              "",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    "Up to Sep 2021",
		"bias_data":             "{Gen=0.005, Rgn=0, Rce=0.005, Age=0.005, Ntn=0.01, Dab=0.015, Sex=0.055, Pap=0.125, Ses=0, Avg=0.024} , ",
	},
	"gpt-3.5-turbo-0125": map[string]any{
		"description":           "A fast, inexpensive model for simple tasks from OpenAI. GPT-3.5 Turbo models can understand and generate natural language or code and have been optimized for chat using the Chat Completions API. The latest GPT-3.5 Turbo model with higher accuracy at responding in requested formats and a fix for a bug which caused a text encoding issue for non-English language function calls. Returns a maximum of 4,096 output tokens. Currently points to gpt-3.5-turbo-0125. 16,385 tokens.",
		"use_case":              "Automated customer service, Content generation, Code documentation",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    "Up to Sep 2021",
		"bias_data":             "{Gen=0.005, Rgn=0, Rce=0.005, Age=0.005, Ntn=0.01, Dab=0.015, Sex=0.055, Pap=0.125, Ses=0, Avg=0.024} , ",
	},
	"gpt-3.5-turbo-0301": map[string]any{
		"description":           "A fast, inexpensive model for simple tasks from OpenAI. GPT-3.5 Turbo models can understand and generate natural language or code and have been optimized for chat using the Chat Completions API. The latest GPT-3.5 Turbo model with higher accuracy at responding in requested formats and a fix for a bug which caused a text encoding issue for non-English language function calls. Returns a maximum of 4,096 output tokens. Currently points to gpt-3.5-turbo-0125. 16,385 tokens.",
		"use_case":              "Automated customer service, Content generation, Code documentation",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    "Up to Sep 2021",
		"bias_data":             "{Gen=0.005, Rgn=0, Rce=0.005, Age=0.005, Ntn=0.01, Dab=0.015, Sex=0.055, Pap=0.125, Ses=0, Avg=0.024} , ",
	},
	"gpt-3.5-turbo-0613": map[string]any{
		"description":           "A fast, inexpensive model for simple tasks from OpenAI.  Its impressive performance makes it suitable for various applications, including virtual assistance, customer service chatbots, and interactive storytelling. The latest GPT-3.5 Turbo model with higher accuracy at responding in requested formats and a fix for a bug which caused a text encoding issue for non-English language function calls. Returns a maximum of 4,096 output tokens. Currently points to gpt-3.5-turbo-0125. 16,385 tokens.",
		"use_case":              "Content generation, Chatbots, Translation",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    "Up to Sep 2021",
		"bias_data":             "{Gen=0.005, Rgn=0, Rce=0.005, Age=0.005, Ntn=0.01, Dab=0.015, Sex=0.055, Pap=0.125, Ses=0, Avg=0.024} , ",
	},
	"gpt-3.5-turbo-instruct-0914": map[string]any{
		"description":           "GPT-3.5-turbo-instruct model is a refined version of the GPT-3, designed to perform natural language tasks with heightened accuracy and reduced toxicity. The GPT-3 models, while revolutionary, had a propensity to generate outputs that could be untruthful or harmful, reflecting the vast and varied nature of their training data sourced from the Internet.",
		"use_case":              "Natural language, Code generation",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    "Up to Sep 2021",
		"bias_data":             "{Gen=0.005, Rgn=0, Rce=0.005, Age=0.005, Ntn=0.01, Dab=0.015, Sex=0.055, Pap=0.125, Ses=0, Avg=0.024} , ",
	},
	"code-davinci-finetune-003": map[string]any{
		"description":           "Babbage and Text Babbage are legacy models and replaced by newer davinci-002, davinci-003 models. These models can understand and generate natural language or code but are not trained with instruction following. These models are made to be replacements for the original GPT-3 base models and use the legacy Completions API. Use GPT-3.5 or GPT-4.",
		"use_case":              "Natural language understanding , Code generation",
		"deprecation_date":      "July 6, 2023",
		"legacy_model":          false,
		"retirement_date":       "July 5, 2024",
		"suggested_replacement": "gpt-35-turbo-instruct",
		"training_till_date":    nil,
		"bias_data":             "{} , ",
	},
	"text-curie-001": map[string]any{
		"description":           "Curie and Text Curie are legacy models and replaced by newer Davinci models. These models can understand and generate natural language or code but are not trained with instruction following. These models are made to be replacements for the original GPT-3 base models and use the legacy Completions API. Use GPT-3.5 or GPT-4.",
		"use_case":              "Natural language understanding , Code generation",
		"deprecation_date":      "July 6, 2023",
		"legacy_model":          true,
		"retirement_date":       "July 5, 2024",
		"suggested_replacement": "text-embedding-3-small",
		"training_till_date":    nil,
		"bias_data":             "{} , ",
	},
	"code-search-babbage-code-001": map[string]any{
		"description":           "Codex is the model that powers GitHub Copilot, which OpenAI built and launched in partnership with GitHub. Proficient in more than a dozen programming languages, Codex can now interpret simple commands in natural language and execute them on the user’s behalf—making it possible to build a natural language interface to existing applications. Cushman is a codex model that is a stronger, multilingual version of the Codex (12B) model in the paper. Code Davinci is a newer version of the model. 2048 tokens.",
		"use_case":              "Code interpretation , Natural language interface",
		"deprecation_date":      "March 6, 2023",
		"legacy_model":          true,
		"retirement_date":       "March 5, 2024",
		"suggested_replacement": "gpt-4-turbo",
		"training_till_date":    nil,
		"bias_data":             "{} , ",
	},
	"tts-1-hd": map[string]any{
		"description":           "A set of models that can convert text into natural sounding spoken audio",
		"use_case":              "Text-to-speech",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    nil,
		"bias_data":             "{} , ",
	},
	"tts-1-hd-1106": map[string]any{
		"description":           "A set of models that can convert text into natural sounding spoken audio",
		"use_case":              "Voice Chatbot, Text-to-speech",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    nil,
		"bias_data":             "{} , ",
	},
	"text-similarity-ada-001": map[string]any{
		"description":           "Ada and Text ADA are legacy models and replaced by Davinci models. These models can understand and generate natural language or code but are not trained with instruction following. These models are made to be replacements for the original GPT-3 base models and use the legacy Completions API. Use GPT-3.5 or GPT-4. 2046 tokens.",
		"use_case":              "Natural language understanding , Code generation , Embedding",
		"deprecation_date":      nil,
		"legacy_model":          true,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    "Jan 2024",
		"bias_data":             "{} , ",
	},
	"ada": map[string]any{
		"description":           "Ada models can understand and generate natural language or code but are not trained with instruction following. Embeddings are a numerical representation of text that can be used to measure the relatedness between two pieces of text. Embeddings are useful for search, clustering, recommendations, anomaly detection, and classification tasks. Use GPT-3.5 or GPT-4. text-embedding-ada-002 is one of the most powerful embedding model. 1536 output dimension.",
		"use_case":              "Natural language understanding , Code generation , Embedding",
		"deprecation_date":      nil,
		"legacy_model":          true,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    "Jan 2024",
		"bias_data":             "{} , ",
	},
	"tts-hd": map[string]any{
		"description":           "A set of models that can convert text into natural sounding spoken audio",
		"use_case":              "Text-to-speech",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    nil,
		"bias_data":             "{} , ",
	},
	"gpt-3.5": map[string]any{
		"description":           "A fast, inexpensive model for simple tasks from OpenAI. GPT-3.5 Turbo models can understand and generate natural language or code and have been optimized for chat using the Chat Completions API. The latest GPT-3.5 Turbo model with higher accuracy at responding in requested formats and a fix for a bug which caused a text encoding issue for non-English language function calls. Returns a maximum of 4,096 output tokens. Currently points to gpt-3.5-turbo-0125. 16,385 tokens.",
		"use_case":              "",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    "Up to Sep 2021",
		"bias_data":             "{Gen=0.005, Rgn=0, Rce=0.005, Age=0.005, Ntn=0.01, Dab=0.015, Sex=0.055, Pap=0.125, Ses=0, Avg=0.024} , ",
	},
	"text-davinci-003": map[string]any{
		"description":           "Babbage and Text Babbage are legacy models and replaced by newer davinci-002, davinci-003 models. These models can understand and generate natural language or code but are not trained with instruction following. These models are made to be replacements for the original GPT-3 base models and use the legacy Completions API. Use GPT-3.5 or GPT-4.",
		"use_case":              "Natural language understanding , Code generation",
		"deprecation_date":      "July 6, 2023",
		"legacy_model":          false,
		"retirement_date":       "July 5, 2024",
		"suggested_replacement": "gpt-35-turbo-instruct",
		"training_till_date":    nil,
		"bias_data":             "{} , ",
	},
	"gpt-4-32k": map[string]any{
		"description":           "GPT-4 is a large multimodal model (accepting text or image inputs and outputting text) that can solve difficult problems with greater accuracy than any of our previous models, thanks to its broader general knowledge and advanced reasoning capabilities. This model was never rolled out widely in favor of GPT-4 Turbo.and this model may be deprecated due to low usage.",
		"use_case":              "",
		"deprecation_date":      nil,
		"legacy_model":          true,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    "Up to Sep 2021",
		"bias_data":             "{} , ",
	},
	"text-davinci-002": map[string]any{
		"description":           "Babbage and Text Babbage are legacy models and replaced by newer davinci-002, davinci-003 models. These models can understand and generate natural language or code but are not trained with instruction following. These models are made to be replacements for the original GPT-3 base models and use the legacy Completions API. Use GPT-3.5 or GPT-4.",
		"use_case":              "Natural language understanding , Code generation",
		"deprecation_date":      "July 6, 2023",
		"legacy_model":          false,
		"retirement_date":       "July 5, 2024",
		"suggested_replacement": "gpt-35-turbo-instruct",
		"training_till_date":    nil,
		"bias_data":             "{} , ",
	},
	"babbage-001": map[string]any{
		"description":           "Babbage and Text Babbage are legacy models and replaced by newer babbage-002 and newer Davinci-002 models. These models can understand and generate natural language or code but are not trained with instruction following. These models are made to be replacements for the original GPT-3 base models and use the legacy Completions API. Use GPT-3.5 or GPT-4.",
		"use_case":              "Natural language understanding , Code generation",
		"deprecation_date":      "July 6, 2023",
		"legacy_model":          false,
		"retirement_date":       "July 5, 2024",
		"suggested_replacement": "gpt-35-turbo-instruct",
		"training_till_date":    "Up to Sep 2021",
		"bias_data":             "{} , ",
	},
	"babbage-002": map[string]any{
		"description":           "These models can understand and generate natural language or code but are not trained with instruction following. With 16384 tokens. Part of the GPT base models these models can understand and generate natural language or code. These models are made to be replacements for the original GPT-3 base models and use the legacy Completions API. Use GPT-3.5 or GPT-4.",
		"use_case":              "Natural language understanding , Code generation",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    "Up to Sep 2021",
		"bias_data":             "{} , ",
	},
	"code-cushman-001": map[string]any{
		"description":           "Codex is the model that powers GitHub Copilot, which OpenAI built and launched in partnership with GitHub. Proficient in more than a dozen programming languages, Codex can now interpret simple commands in natural language and execute them on the user’s behalf—making it possible to build a natural language interface to existing applications. Cushman is a codex model that is a stronger, multilingual version of the Codex (12B) model in the paper. Code Davinci is a newer version of the model. 2048 tokens.",
		"use_case":              "Code interpretation , Natural language interface",
		"deprecation_date":      "March 6, 2023",
		"legacy_model":          true,
		"retirement_date":       "March 5, 2024",
		"suggested_replacement": "gpt-4-turbo",
		"training_till_date":    nil,
		"bias_data":             "{} , ",
	},
	"gpt-3.5-turbo": map[string]any{
		"description":           "A fast, inexpensive model for simple tasks from OpenAI. GPT-3.5 Turbo models can understand and generate natural language or code and have been optimized for chat using the Chat Completions API. The latest GPT-3.5 Turbo model with higher accuracy at responding in requested formats and a fix for a bug which caused a text encoding issue for non-English language function calls. Returns a maximum of 4,096 output tokens. Currently points to gpt-3.5-turbo-0125. 16,385 tokens.",
		"use_case":              "",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    "Up to Sep 2021",
		"bias_data":             "{Gen=0.005, Rgn=0, Rce=0.005, Age=0.005, Ntn=0.01, Dab=0.015, Sex=0.055, Pap=0.125, Ses=0, Avg=0.024} , ",
	},
	"gpt-4-turbo-2024-04-09": map[string]any{
		"description":           "GPT-4 is a large multimodal model (accepting text or image inputs and outputting text) that can solve difficult problems with greater accuracy than any of our previous models, thanks to its broader general knowledge and advanced reasoning capabilities. GPT-4 Turbo is the previous set of high-intelligence models before gpt-4o. The latest GPT-4 Turbo model with vision capabilities. With 128,000 tokens. Currently points to gpt-4-turbo-2024-04-09.",
		"use_case":              "",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    "Up to Dec 2023",
		"bias_data":             "{} , ",
	},
	"gpt-3.5-turbo-instruct": map[string]any{
		"description":           "A fast, inexpensive model for simple tasks from OpenAI. GPT-3.5 Turbo models can understand and generate natural language or code and have been optimized for chat using the Chat Completions API. Similar capabilities as GPT-3 era models. Compatible with legacy Completions endpoint and not Chat Completions. 4,096 tokens.",
		"use_case":              "",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    "Up to Sep 2021",
		"bias_data":             "{Gen=0.005, Rgn=0, Rce=0.005, Age=0.005, Ntn=0.01, Dab=0.015, Sex=0.055, Pap=0.125, Ses=0, Avg=0.024} , ",
	},
	"dall-e-3": map[string]any{
		"description":           "A model that can generate and edit images given a natural language prompt. DALL·E is a AI system that can create realistic images and art from a description in natural language. DALL·E 3 currently supports the ability, given a prompt, to create a new image with a specific size. The API incorporates built-in moderation to help developers protect their applications against misuse. Upto 400 characters input.",
		"use_case":              "Image generation , Art creation",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    "Up to Nov 2023",
		"bias_data":             "{} , ",
	},
	"gpt-4o": map[string]any{
		"description":           "GPT-4 is a large multimodal model (accepting text or image inputs and outputting text) that can solve difficult problems with greater accuracy than any of our previous models, thanks to its broader general knowledge and advanced reasoning capabilities. The fastest and most affordable flagship model of GPT4 family from OpenAI is GPT-4o. Currently points to gpt-4o-2024-05-13 model. With 128,000 tokens. As more models are trained the base model for got-40 can change.",
		"use_case":              "",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    "Up to Oct 2023",
		"bias_data":             "{} , ",
	},
	"dall-e-2": map[string]any{
		"description":           "A model that can generate and edit images given a natural language prompt. DALL·E is a AI system that can create realistic images and art from a description in natural language. DALL·E 3 currently supports the ability, given a prompt, to create a new image with a specific size. The API incorporates built-in moderation to help developers protect their applications against misuse. Upto 400 characters input.",
		"use_case":              "Image generation , Art creation",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    "Up to Nov 2023",
		"bias_data":             "{} , ",
	},
	"code-search-babbage-text-001": map[string]any{
		"description":           "Codex is the model that powers GitHub Copilot, which OpenAI built and launched in partnership with GitHub. Proficient in more than a dozen programming languages, Codex can now interpret simple commands in natural language and execute them on the user’s behalf—making it possible to build a natural language interface to existing applications. Cushman is a codex model that is a stronger, multilingual version of the Codex (12B) model in the paper. Code Davinci is a newer version of the model. 2048 tokens.",
		"use_case":              "Code interpretation , Natural language interface",
		"deprecation_date":      "March 6, 2023",
		"legacy_model":          true,
		"retirement_date":       "March 5, 2024",
		"suggested_replacement": "gpt-4-turbo",
		"training_till_date":    nil,
		"bias_data":             "{} , ",
	},
	"davinci": map[string]any{
		"description":           "Babbage and Text Babbage are legacy models and replaced by newer davinci-002, davinci-003 models. These models can understand and generate natural language or code but are not trained with instruction following. These models are made to be replacements for the original GPT-3 base models and use the legacy Completions API. Use GPT-3.5 or GPT-4.",
		"use_case":              "Natural language understanding , Code generation",
		"deprecation_date":      "July 6, 2023",
		"legacy_model":          false,
		"retirement_date":       "July 5, 2024",
		"suggested_replacement": "gpt-35-turbo-instruct",
		"training_till_date":    nil,
		"bias_data":             "{} , ",
	},
	"gpt-3.5-turbo-16k-0613": map[string]any{
		"description":           "A fast, inexpensive model for simple tasks from OpenAI. GPT-3.5 Turbo models can understand and generate natural language or code and have been optimized for chat using the Chat Completions API. Older turbo model with good accuracy at responding in requested formats and a fix for a bug which caused a text encoding issue for non-English language function calls. The 16k models have a bigger input window of tokens allowed - 16,385 tokens. Points to gpt-3.5-turbo-16k-0613",
		"use_case":              "",
		"deprecation_date":      nil,
		"legacy_model":          true,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    "Up to Sep 2021",
		"bias_data":             "{Gen=0.005, Rgn=0, Rce=0.005, Age=0.005, Ntn=0.01, Dab=0.015, Sex=0.055, Pap=0.125, Ses=0, Avg=0.024} , ",
	},
	"text-davinci-fine-tune-002": map[string]any{
		"description":           "Babbage and Text Babbage are legacy models and replaced by newer davinci-002, davinci-003 models. These models can understand and generate natural language or code but are not trained with instruction following. These models are made to be replacements for the original GPT-3 base models and use the legacy Completions API. Use GPT-3.5 or GPT-4.",
		"use_case":              "Natural language understanding , Code generation",
		"deprecation_date":      "July 6, 2023",
		"legacy_model":          false,
		"retirement_date":       "July 5, 2024",
		"suggested_replacement": "gpt-35-turbo-instruct",
		"training_till_date":    nil,
		"bias_data":             "{} , ",
	},
	"gpt-4-turbo": map[string]any{
		"description":           "GPT-4 is a large multimodal model (accepting text or image inputs and outputting text) that can solve difficult problems with greater accuracy than any of our previous models, thanks to its broader general knowledge and advanced reasoning capabilities. GPT-4 Turbo is the previous set of high-intelligence models before gpt-4o. The latest GPT-4 Turbo model with vision capabilities. With 128,000 tokens. Currently points to gpt-4-turbo-2024-04-09.",
		"use_case":              "",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    "Up to Dec 2023",
		"bias_data":             "{} , ",
	},
	"gpt-4-0613": map[string]any{
		"description":           "GPT-4 is a large multimodal model (accepting text or image inputs and outputting text) that can solve difficult problems with greater accuracy than any of our previous models, thanks to its broader general knowledge and advanced reasoning capabilities. Currently points to gpt-4-0613. 8192 tokens.",
		"use_case":              "Health , Cooking , Speed of output",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    "Up to Sep 2021",
		"bias_data":             "{} , ",
	},
	"gpt-4-turbo-preview": map[string]any{
		"description":           "GPT-4 is a large multimodal model (accepting text or image inputs and outputting text) that can solve difficult problems with greater accuracy than any of our previous models, thanks to its broader general knowledge and advanced reasoning capabilities. Currently points to gpt-4-0613. 8192 tokens.",
		"use_case":              "Health , Cooking , Speed of output",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    "Up to Dec 2023",
		"bias_data":             "{} , ",
	},
	"gpt-4-0125-preview": map[string]any{
		"description":           "GPT-4 is a large multimodal model (accepting text or image inputs and outputting text) that can solve difficult problems with greater accuracy than any of our previous models, thanks to its broader general knowledge and advanced reasoning capabilities. Currently points to gpt-4-0613. 8192 tokens.",
		"use_case":              "Long-form content generation, Highly accurate text completion, Advanced conversational agent",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    "Up to Dec 2023",
		"bias_data":             "{} , ",
	},
	"gpt-4-1106-preview": map[string]any{
		"description":           "GPT-4 is a large multimodal model (accepting text or image inputs and outputting text) that can solve difficult problems with greater accuracy than any of our previous models, thanks to its broader general knowledge and advanced reasoning capabilities. Currently points to gpt-4-0613. 8192 tokens.",
		"use_case":              "High-context conversational AI, Advanced text generation, Complex task handling",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    "Up to Apr 2023",
		"bias_data":             "{} , ",
	},
	"gpt-4-1106-vision-preview": map[string]any{
		"description":           "GPT-4 Turbo with Vision is a large multimodal model (LMM) developed by OpenAI that can analyze images and provide textual responses to questions about them. It incorporates both natural language processing and visual understanding. The GPT-4 Turbo with Vision model answers general questions about what's present in images.",
		"use_case":              "Image Translation, Image Generation, Analysis Image",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    "Up to Apr 2023",
		"bias_data":             "{} , ",
	},
	"gpt-4-vision-preview": map[string]any{
		"description":           "GPT-4 Turbo with Vision is a large multimodal model (LMM) developed by OpenAI that can analyze images and provide textual responses to questions about them. It incorporates both natural language processing and visual understanding. The GPT-4 Turbo with Vision model answers general questions about what's present in images.",
		"use_case":              "Image Translation, Image Generation, Analysis Image",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    "Up to Apr 2023",
		"bias_data":             "{} , ",
	},
	"Meta-Llama-3-8B-Instruct": map[string]any{
		"description":           "Meta developed and released the Meta Llama 3 family of large language models (LLMs), a collection of pretrained and instruction tuned generative text models in 8 and 70B sizes. The Llama 3 instruction tuned models are optimized for dialogue use cases and outperform many of the available open source chat models on common industry benchmarks.",
		"use_case":              "",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    "Up to March 2023",
		"bias_data":             "{Gen=0.045, Rgn=0.125, Rce=0.34, Age=0.055, Ntn=0.35, Dab=0.15, Sex=0.27, Pap=0.14, Ses=0.025, Avg=0.167} , ",
	},
	"tts-1": map[string]any{
		"description":           "A set of models that can convert text into natural sounding spoken audio",
		"use_case":              "Text-to-speech",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    nil,
		"bias_data":             "{} , ",
	},
	"tts-1-1106": map[string]any{
		"description":           "A set of models that can convert text into natural sounding spoken audio",
		"use_case":              "Text-to-speech",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    nil,
		"bias_data":             "{} , ",
	},
	"gpt-4o-2024-05-13": map[string]any{
		"description":           "GPT-4 is a large multimodal model (accepting text or image inputs and outputting text) that can solve difficult problems with greater accuracy than any of our previous models, thanks to its broader general knowledge and advanced reasoning capabilities. The fastest and most affordable flagship model of GPT4 family from OpenAI is GPT-4o. Currently points to gpt-4o-2024-05-13 model. With 128,000 tokens. As more models are trained the base model for got-40 can change.",
		"use_case":              "",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    "Up to Oct 2023",
		"bias_data":             "{} , ",
	},
	"gpt-4o-2024-08-06": map[string]any{
		"description":           "GPT-4 is a large multimodal model (accepting text or image inputs and outputting text) that can solve difficult problems with greater accuracy than any of our previous models, thanks to its broader general knowledge and advanced reasoning capabilities. The fastest and most affordable flagship model of GPT4 family from OpenAI is GPT-4o. Currently points to gpt-4o-2024-05-13 model. With 128,000 tokens. As more models are trained the base model for got-40 can change.",
		"use_case":              "",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    "Up to Oct 2023",
		"bias_data":             "{} , ",
	},
	"text-embedding-3-small": map[string]any{
		"description":           "A set of models that can convert text into a numerical form. Embeddings are a numerical representation of text that can be used to measure the relatedness between two pieces of text. Embeddings are useful for search, clustering, recommendations, anomaly detection, and classification tasks. Increased performance over 2nd generation ada embedding model. 1,536 output dimension.",
		"use_case":              "Text embedding , Natural language processing",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    "Jan 2024",
		"bias_data":             "{} , ",
	},
	"babbage": map[string]any{
		"description":           "Babbage and Text Babbage are legacy models and replaced by newer babbage-002 and newer Davinci-002 models. These models can understand and generate natural language or code but are not trained with instruction following. These models are made to be replacements for the original GPT-3 base models and use the legacy Completions API. Use GPT-3.5 or GPT-4.",
		"use_case":              "Natural language understanding , Code generation",
		"deprecation_date":      "July 6, 2023",
		"legacy_model":          false,
		"retirement_date":       "July 5, 2024",
		"suggested_replacement": "gpt-35-turbo-instruct",
		"training_till_date":    "Up to Sep 2021",
		"bias_data":             "{} , ",
	},
	"text-ada-001": map[string]any{
		"description":           "Ada and Text ADA are legacy models and replaced by Davinci models. These models can understand and generate natural language or code but are not trained with instruction following. These models are made to be replacements for the original GPT-3 base models and use the legacy Completions API. Use GPT-3.5 or GPT-4. 2046 tokens.",
		"use_case":              "Natural language understanding , Code generation , Embedding",
		"deprecation_date":      nil,
		"legacy_model":          true,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    "Jan 2024",
		"bias_data":             "{} , ",
	},
	"code-cushman-fine-tune-002": map[string]any{
		"description":           "Codex is the model that powers GitHub Copilot, which OpenAI built and launched in partnership with GitHub. Proficient in more than a dozen programming languages, Codex can now interpret simple commands in natural language and execute them on the user’s behalf—making it possible to build a natural language interface to existing applications. Cushman is a codex model that is a stronger, multilingual version of the Codex (12B) model in the paper. Code Davinci is a newer version of the model. 2048 tokens.",
		"use_case":              "Code interpretation , Natural language interface",
		"deprecation_date":      "March 6, 2023",
		"legacy_model":          true,
		"retirement_date":       "March 5, 2024",
		"suggested_replacement": "gpt-4-turbo",
		"training_till_date":    nil,
		"bias_data":             "{} , ",
	},
	"gpt-4": map[string]any{
		"description":           "GPT-4 is a large multimodal model (accepting text or image inputs and outputting text) that can solve difficult problems with greater accuracy than any of our previous models, thanks to its broader general knowledge and advanced reasoning capabilities. Currently points to gpt-4-0613. 8192 tokens.",
		"use_case":              "Health , Cooking , Speed of output",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    "Up to Sep 2021",
		"bias_data":             "{} , ",
	},
	"text-embedding-ada-002": map[string]any{
		"description":           "A set of models that can convert text into a numerical form. Embeddings are a numerical representation of text that can be used to measure the relatedness between two pieces of text. Embeddings are useful for search, clustering, recommendations, anomaly detection, and classification tasks. Increased performance over other embedding models and uses 3,072 output dimension.",
		"use_case":              "Text embedding , Natural language processing",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    "Jan 2024",
		"bias_data":             "{} , ",
	},
	"tts": map[string]any{
		"description":           "A set of models that can convert text into natural sounding spoken audio",
		"use_case":              "Text-to-speech",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    nil,
		"bias_data":             "{} , ",
	},
	"curie": map[string]any{
		"description":           "Curie and Text Curie are legacy models and replaced by newer Davinci models. These models can understand and generate natural language or code but are not trained with instruction following. These models are made to be replacements for the original GPT-3 base models and use the legacy Completions API. Use GPT-3.5 or GPT-4.",
		"use_case":              "Natural language understanding , Code generation",
		"deprecation_date":      "July 6, 2023",
		"legacy_model":          true,
		"retirement_date":       "July 5, 2024",
		"suggested_replacement": "text-embedding-3-small",
		"training_till_date":    nil,
		"bias_data":             "{} , ",
	},
	"text-similarity-curie-001": map[string]any{
		"description":           "Curie and Text Curie are legacy models and replaced by newer Davinci models. These models can understand and generate natural language or code but are not trained with instruction following. These models are made to be replacements for the original GPT-3 base models and use the legacy Completions API. Use GPT-3.5 or GPT-4.",
		"use_case":              "Natural language understanding , Code generation",
		"deprecation_date":      "July 6, 2023",
		"legacy_model":          true,
		"retirement_date":       "July 5, 2024",
		"suggested_replacement": "text-embedding-3-small",
		"training_till_date":    nil,
		"bias_data":             "{} , ",
	},
	"code-davinci-fine-tune-002": map[string]any{
		"description":           "Codex is the model that powers GitHub Copilot, which OpenAI built and launched in partnership with GitHub. Proficient in more than a dozen programming languages, Codex can now interpret simple commands in natural language and execute them on the user’s behalf—making it possible to build a natural language interface to existing applications. Cushman is a codex model that is a stronger, multilingual version of the Codex (12B) model in the paper. Code Davinci is a newer version of the model. 2048 tokens.",
		"use_case":              "Code interpretation , Natural language interface",
		"deprecation_date":      "March 6, 2023",
		"legacy_model":          true,
		"retirement_date":       "March 5, 2024",
		"suggested_replacement": "gpt-4-turbo",
		"training_till_date":    nil,
		"bias_data":             "{} , ",
	},
	"code-search-ada-code-001": map[string]any{
		"description":           "Codex is the model that powers GitHub Copilot, which OpenAI built and launched in partnership with GitHub. Proficient in more than a dozen programming languages, Codex can now interpret simple commands in natural language and execute them on the user’s behalf—making it possible to build a natural language interface to existing applications. Cushman is a codex model that is a stronger, multilingual version of the Codex (12B) model in the paper. Code Davinci is a newer version of the model. 2048 tokens.",
		"use_case":              "Code interpretation , Natural language interface",
		"deprecation_date":      "March 6, 2023",
		"legacy_model":          true,
		"retirement_date":       "March 5, 2024",
		"suggested_replacement": "gpt-4-turbo",
		"training_till_date":    nil,
		"bias_data":             "{} , ",
	},
	"whisper": map[string]any{
		"description":           "A model that can convert audio into text. Whisper is a general-purpose speech recognition model. It is trained on a large dataset of diverse audio and is also a multi-task model that can perform multilingual speech recognition as well as speech translation and language identification. The Whisper v2-large model is currently available through API with the whisper-1 model name. This model is no different from the open source version of Whisper. The key value of Whisper is that in a zero-shot transfer setting without the need for any fine-tuning the models perform very well. Max inputs 25MB file.",
		"use_case":              "Speech recognition , Speech translation , Language identification",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    nil,
		"bias_data":             "{} , ",
	},
	"whisper-1": map[string]any{
		"description":           "A model that can convert audio into text. Whisper is a general-purpose speech recognition model. It is trained on a large dataset of diverse audio and is also a multi-task model that can perform multilingual speech recognition as well as speech translation and language identification. The Whisper v2-large model is currently available through API with the whisper-1 model name. This model is no different from the open source version of Whisper. The key value of Whisper is that in a zero-shot transfer setting without the need for any fine-tuning the models perform very well. Max inputs 25MB file.",
		"use_case":              "Speech recognition , Speech translation , Language identification, Generate Audio Content, Voice assistants",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    nil,
		"bias_data":             "{} , ",
	},
	"gpt-3.5-turbo-16k": map[string]any{
		"description":           "A fast, inexpensive model for simple tasks from OpenAI. GPT-3.5 Turbo models can understand and generate natural language or code and have been optimized for chat using the Chat Completions API. Older turbo model with good accuracy at responding in requested formats and a fix for a bug which caused a text encoding issue for non-English language function calls. The 16k models have a bigger input window of tokens allowed - 16,385 tokens. Points to gpt-3.5-turbo-16k-0613",
		"use_case":              "",
		"deprecation_date":      nil,
		"legacy_model":          true,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    "Up to Sep 2021",
		"bias_data":             "{Gen=0.005, Rgn=0, Rce=0.005, Age=0.005, Ntn=0.01, Dab=0.015, Sex=0.055, Pap=0.125, Ses=0, Avg=0.024} , ",
	},
	"o1-preview": map[string]any{
		"description":           "OpenAI's o1-preview is an advanced reasoning model designed to tackle complex problems in science, mathematics, and programming. It employs chain-of-thought prompting and reinforcement learning to break down intricate tasks, outperforming previous models like GPT-4o in various benchmarks.",
		"use_case":              "Advanced problem-solving, scientific research, mathematical computations, coding assistance, data analysis, and educational applications requiring deep reasoning.",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": "o1",
		"training_till_date":    "Up to Sep 2024",
		"bias_data":             "{}",
	},
	"o1-preview-2024-09-12": map[string]any{
		"description":           "OpenAI's o1-preview is an advanced reasoning model designed to tackle complex problems in science, mathematics, and programming. It employs chain-of-thought prompting and reinforcement learning to break down intricate tasks, outperforming previous models like GPT-4o in various benchmarks.",
		"use_case":              "Advanced problem-solving, scientific research, mathematical computations, coding assistance, data analysis, and educational applications requiring deep reasoning.",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": "o1",
		"training_till_date":    "Up to Sep 2024",
		"bias_data":             "{}",
	},
	"o1-pro": map[string]any{
		"description":           "OpenAI's o1-pro is a high-performance AI model designed for advanced reasoning tasks. It utilizes increased computational resources to provide more accurate and comprehensive responses, particularly in complex domains such as mathematics, science, and programming.",
		"use_case":              "Advanced data analysis, complex problem-solving, scientific research, legal analysis, and applications requiring in-depth reasoning.",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    "Up to Oct 2023",
		"bias_data":             "{}",
	},
	"o1-pro-2025-03-19": map[string]any{
		"description":           "OpenAI's o1-pro is a high-performance AI model designed for advanced reasoning tasks. It utilizes increased computational resources to provide more accurate and comprehensive responses, particularly in complex domains such as mathematics, science, and programming.",
		"use_case":              "Advanced data analysis, complex problem-solving, scientific research, legal analysis, and applications requiring in-depth reasoning.",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    "Up to Oct 2023",
		"bias_data":             "{}",
	},
	"o3-mini": map[string]any{
		"description":           "OpenAI's o3-mini is a cost-efficient reasoning model optimized for tasks requiring logical thinking, such as coding, mathematics, and scientific problem-solving. It offers adjustable reasoning levels—low, medium, and high—to balance speed and depth, making it suitable for a range of applications.",
		"use_case":              "Coding assistance, mathematical problem-solving, scientific analysis, logic-based tasks, educational tools, and applications requiring structured reasoning.",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": "o4-mini",
		"training_till_date":    "Up to Oct 2023",
		"bias_data":             "{}",
	},
	"o3-mini-2025-01-31": map[string]any{
		"description":           "OpenAI's o3-mini is a cost-efficient reasoning model optimized for tasks requiring logical thinking, such as coding, mathematics, and scientific problem-solving. It offers adjustable reasoning levels—low, medium, and high—to balance speed and depth, making it suitable for a range of applications.",
		"use_case":              "Coding assistance, mathematical problem-solving, scientific analysis, logic-based tasks, educational tools, and applications requiring structured reasoning.",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": "o4-mini",
		"training_till_date":    "Up to Oct 2023",
		"bias_data":             "{}",
	},
	"omni-moderation-2024-09-26": map[string]any{
		"description":           "Omni Moderation 2024-09-26 is OpenAI's advanced multimodal content moderation model, capable of analyzing both text and images to detect potentially harmful content. Built on the GPT-4o architecture, it offers enhanced accuracy, especially in non-English languages, and supports a broader range of content categories.",
		"use_case":              "Content moderation for social media platforms, user-generated content platforms, AI-generated content safety checks, automated moderation systems, and applications requiring detection of harmful content in text and images.",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": "omni-moderation-latest",
		"training_till_date":    "Up to Sep 2024",
		"bias_data":             "{}",
	},
	"omni-moderation-latest": map[string]any{
		"description":           "Omni Moderation latest is OpenAI's advanced multimodal content moderation model, capable of analyzing both text and images to detect potentially harmful content. Built on the GPT-4o architecture, it offers enhanced accuracy, especially in non-English languages, and supports a broader range of content categories.",
		"use_case":              "Content moderation for social media platforms, user-generated content platforms, AI-generated content safety checks, automated moderation systems, and applications requiring detection of harmful content in text and images.",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    "Up to Sep 2024",
		"bias_data":             "{}",
	},
	"codex-mini-latest": map[string]any{
		"description":           "Codex Mini Latest is a lightweight, fine-tuned version of OpenAI's o4-mini model, specifically optimized for low-latency code editing and question-answering tasks. Designed for integration with the Codex CLI, it offers efficient performance for developers seeking quick and responsive coding assistance.",
		"use_case":              "Ideal for real-time code editing, debugging, and answering code-related queries within terminal environments. Particularly suited for developers using the Codex CLI for streamlined coding workflows.",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    "Up to May 2024",
		"bias_data":             "{}",
	},
	"chatgpt-4o-latest": map[string]any{
		"description":           "Latest ChatGPT model based on GPT-4o, supporting multimodal inputs (text, audio, image) and outputs with improved speed and capabilities.",
		"use_case":              "General-purpose assistant, content creation, customer support, multimodal tasks",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    "Up to Oct 2023",
		"bias_data":             "{}",
	},
	"gpt-4o-search-preview": map[string]any{
		"description":           "GPT-4o Search Preview is a specialized variant of OpenAI's GPT-4o model, designed to integrate real-time web search capabilities within the Chat Completions API. It combines GPT-4o's natural language understanding with live web access, enabling up-to-date, context-aware responses grounded in current data.",
		"use_case":              "Real-time information retrieval, fact-checking, news summarization, dynamic content generation, research assistance, and applications requiring current event awareness.",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    "Up to Oct 2023",
		"bias_data":             "{}",
	},
	"gpt-4o-search-preview-2025-03-11": map[string]any{
		"description":           "GPT-4o Search Preview is a specialized variant of OpenAI's GPT-4o model, designed to integrate real-time web search capabilities within the Chat Completions API. It combines GPT-4o's natural language understanding with live web access, enabling up-to-date, context-aware responses grounded in current data.",
		"use_case":              "Real-time information retrieval, fact-checking, news summarization, dynamic content generation, research assistance, and applications requiring current event awareness.",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    "Up to Oct 2023",
		"bias_data":             "{}",
	},
	"gpt-4o-mini-realtime-preview": map[string]any{
		"description":           "Preview of GPT-4o mini model designed for real-time interactions, supporting low-latency text and audio inputs and outputs.",
		"use_case":              "Live customer support, real-time translation, interactive voice applications",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": "gpt-4o-mini-realtime-preview-2024-12-17",
		"training_till_date":    "Up to Oct 2023",
		"bias_data":             "{}",
	},
	"gpt-4o-mini-realtime-preview-2024-12-17": map[string]any{
		"description":           "Preview of GPT-4o mini model designed for real-time interactions, supporting low-latency text and audio inputs and outputs.",
		"use_case":              "Live customer support, real-time translation, interactive voice applications",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    "Up to Oct 2023",
		"bias_data":             "{}",
	},
	"gpt-4.1-nano": map[string]any{
		"description":           "Fastest and most affordable GPT-4.1 variant, designed for tasks requiring low latency such as classification or autocompletion.",
		"use_case":              "Real-time classification, autocomplete features, lightweight applications",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    "Up to May 31, 2024",
		"bias_data":             "{}",
	},
	"gpt-4.1-nano-2025-04-14": map[string]any{
		"description":           "Fastest and most affordable GPT-4.1 variant, designed for tasks requiring low latency such as classification or autocompletion.",
		"use_case":              "Real-time classification, autocomplete features, lightweight applications",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    "Up to May 31, 2024",
		"bias_data":             "{}",
	},
	"gpt-4o-mini-search-preview": map[string]any{
		"description":           "Specialized GPT-4o mini model trained to understand and execute web search queries via the Chat Completions API.",
		"use_case":              "Web search integration, information retrieval, search query understanding",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": "gpt-4o-mini-search-preview-2025-03-11",
		"training_till_date":    "Up to Oct 2023",
		"bias_data":             "{}",
	},
	"gpt-4o-mini-search-preview-2025-03-11": map[string]any{
		"description":           "GPT-4o mini Search Preview is a specialized model trained to understand and execute web search queries with the Chat Completions API. It is trained to understand and execute web search queries via the Chat Completions API.",
		"use_case":              "Web search integration, information retrieval, search query understanding",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": "gpt-4o-mini-search-preview-2025-03-11",
		"training_till_date":    "Up to Oct 2023",
		"bias_data":             "{}",
	},
	"gpt-4o-realtime-preview": map[string]any{
		"description":           "GPT-4o model capable of real-time text and audio inputs and outputs, designed for low-latency conversational interactions.",
		"use_case":              "Live chat interfaces, real-time transcription, voice assistants",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    "Up to Oct 2023",
		"bias_data":             "{}",
	},
	"gpt-4o-realtime-preview-2024-10-01": map[string]any{
		"description":           "This is a preview release of the GPT-4o Realtime model capable of real-time text and audio inputs and outputs, designed for low-latency conversational interactions.",
		"use_case":              "Live chat interfaces, real-time transcription, voice assistants",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": "gpt-4o-realtime-preview-2024-12-17",
		"training_till_date":    "Up to Oct 2023",
		"bias_data":             "{}",
	},
	"gpt-4o-realtime-preview-2024-12-17": map[string]any{
		"description":        "This is a preview release of the GPT-4o Realtime modelcapable of real-time text and audio inputs and outputs, designed for low-latency conversational interactions.",
		"use_case":           "Live chat interfaces, real-time transcription, voice assistants",
		"deprecation_date":   nil,
		"legacy_model":       false,
		"retirement_date":    nil,
		"training_till_date": "Up to Oct 2023",
		"bias_data":          "{}",
	},
	"gpt-4o-realtime-preview-2025-06-03": map[string]any{
		"description":        "This is a preview release of the GPT-4o Realtime model capable of real-time text and audio inputs and outputs, designed for low-latency conversational interactions.",
		"use_case":           "Live chat interfaces, real-time transcription, voice assistants",
		"deprecation_date":   nil,
		"legacy_model":       false,
		"retirement_date":    nil,
		"training_till_date": "Up to Apr 2024",
		"bias_data":          "{}",
	},
	"gpt-3.5-turbo-1106": map[string]any{
		"description":           "GPT-3.5 Turbo model released in November 2023, offering improved performance over previous versions.",
		"use_case":              "General-purpose tasks, content generation, conversational AI",
		"deprecation_date":      nil,
		"legacy_model":          true,
		"retirement_date":       nil,
		"suggested_replacement": "gpt-4o",
		"training_till_date":    "Up to Oct 2023",
		"bias_data":             "{}",
	},
	"gpt-4.1": map[string]any{
		"description":           "GPT-4.1 model featuring improved coding capabilities, long context comprehension, and instruction following.",
		"use_case":              "Advanced coding tasks, long document summarization, complex instruction execution",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    "Up to June 2024",
		"bias_data":             "{}",
	},
	"gpt-4.1-2025-04-14": map[string]any{
		"description":           "GPT-4.1 model featuring improved coding capabilities, long context comprehension, and instruction following.",
		"use_case":              "Advanced coding tasks, long document summarization, complex instruction execution",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    "Up to June 2024",
		"bias_data":             "{}",
	},
	"gpt-4.5-preview": map[string]any{
		"description":           "Preview version of GPT-4.5, known for its intuitive and thoughtful responses, suitable for creative and professional conversational tasks.",
		"use_case":              "Creative writing, professional communication, nuanced dialogue",
		"deprecation_date":      "2025-07-14",
		"legacy_model":          true,
		"retirement_date":       "2025-07-14",
		"suggested_replacement": "gpt-4.1",
		"training_till_date":    "Up to Oct 2023",
		"bias_data":             "{}",
	},
	"gpt-4.5-preview-2025-02-27": map[string]any{
		"description":           "Preview version of GPT-4.5, known for its intuitive and thoughtful responses, suitable for creative and professional conversational tasks.",
		"use_case":              "Creative writing, professional communication, nuanced dialogue",
		"deprecation_date":      "2025-07-14",
		"legacy_model":          true,
		"retirement_date":       "2025-07-14",
		"suggested_replacement": "gpt-4.1",
		"training_till_date":    "Up to Oct 2023",
		"bias_data":             "{}",
	},
	"o1": map[string]any{
		"description":           "OpenAI's first model with enhanced reasoning abilities, designed to solve complex problems by processing queries step-by-step.",
		"use_case":              "Advanced problem-solving in science, coding, and mathematics",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    "Up to Oct 2023",
		"bias_data":             "{}",
	},
	"o1-2024-12-17": map[string]any{
		"description":           "OpenAI's first model with enhanced reasoning abilities, designed to solve complex problems by processing queries step-by-step.",
		"use_case":              "Advanced problem-solving in science, coding, and mathematics",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    "Up to Oct 2023",
		"bias_data":             "{}",
	},
	"o1-mini": map[string]any{
		"description":           "Cost-efficient reasoning model optimized for STEM tasks, offering performance comparable to o1 on benchmarks like AIME and Codeforces.",
		"use_case":              "STEM reasoning, coding, math problem-solving",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    "Up to Oct 2023",
		"bias_data":             "{}",
	},
	"o1-mini-2024-09-12": map[string]any{
		"description":           "Cost-efficient reasoning model optimized for STEM tasks, offering performance comparable to o1 on benchmarks like AIME and Codeforces.",
		"use_case":              "STEM reasoning, coding, math problem-solving",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    "Up to Oct 2023",
		"bias_data":             "{}",
	},
	"o4-mini": map[string]any{
		"description":           "OpenAI's reasoning model capable of processing both text and images, designed to enhance decision-making across various sectors.",
		"use_case":              "Data analysis, healthcare diagnostics, financial risk assessment",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    "Up to Apr 2025",
		"bias_data":             "{}",
	},
	"o4-mini-2025-04-16": map[string]any{
		"description":           "OpenAI's reasoning model capable of processing both text and images, designed to enhance decision-making across various sectors.",
		"use_case":              "Data analysis, healthcare diagnostics, financial risk assessment",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    "Up to Apr 2025",
		"bias_data":             "{}",
	},
	"gpt-4o-audio": map[string]any{
		"description":           "GPT-4o Audio is OpenAI's advanced multimodal model capable of processing and generating text, audio, and images in real-time. It offers rapid response times (~320 milliseconds), supports over 50 languages, and integrates text, vision, and audio processing within a single neural network, enabling natural and expressive voice interactions.",
		"use_case":              "Voice assistants, real-time transcription, multilingual translation, interactive voice applications, customer support, creative storytelling",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    "Up to Oct 2023",
		"bias_data":             "{}",
	},
	"gpt-4o-audio-preview": map[string]any{
		"description":           "This is a preview release of the GPT-4o Audio models.GPT-4o Audio is OpenAI's advanced multimodal model capable of processing and generating text, audio, and images in real-time. It offers rapid response times (~320 milliseconds), supports over 50 languages, and integrates text, vision, and audio processing within a single neural network, enabling natural and expressive voice interactions.",
		"use_case":              "Voice assistants, real-time transcription, multilingual translation, interactive voice applications, customer support, creative storytelling",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    "Up to Oct 2023",
		"bias_data":             "{}",
	},
	"gpt-4o-audio-preview-2024-10-01": map[string]any{
		"description":           "This is a preview release of the GPT-4o Audio models.GPT-4o Audio is OpenAI's advanced multimodal model capable of processing and generating text, audio, and images in real-time. It offers rapid response times (~320 milliseconds), supports over 50 languages, and integrates text, vision, and audio processing within a single neural network, enabling natural and expressive voice interactions.",
		"use_case":              "Voice assistants, real-time transcription, multilingual translation, interactive voice applications, customer support, creative storytelling",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    "Up to Oct 2023",
		"bias_data":             "{}",
	},
	"gpt-4o-audio-preview-2024-12-17": map[string]any{
		"description":           "This is a preview release of the GPT-4o Audio models.GPT-4o Audio is OpenAI's advanced multimodal model capable of processing and generating text, audio, and images in real-time. It offers rapid response times (~320 milliseconds), supports over 50 languages, and integrates text, vision, and audio processing within a single neural network, enabling natural and expressive voice interactions.",
		"use_case":              "Voice assistants, real-time transcription, multilingual translation, interactive voice applications, customer support, creative storytelling",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    "Up to Oct 2023",
		"bias_data":             "{}",
	},
	"gpt-4o-audio-preview-2025-06-03": map[string]any{
		"description":           "This is a preview release of the GPT-4o Audio models.GPT-4o Audio is OpenAI's advanced multimodal model capable of processing and generating text, audio, and images in real-time. It offers rapid response times (~320 milliseconds), supports over 50 languages, and integrates text, vision, and audio processing within a single neural network, enabling natural and expressive voice interactions.",
		"use_case":              "Voice assistants, real-time transcription, multilingual translation, interactive voice applications, customer support, creative storytelling",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    "Up to Oct 2023",
		"bias_data":             "{}",
	},
	"gpt-4o-mini-audio": map[string]any{
		"description":           "GPT-4o Mini Audio is a lightweight, cost-effective multimodal model developed by OpenAI, capable of processing and generating both text and audio. It offers high-quality audio interactions at a fraction of the cost of the full GPT-4o audio models, making it ideal for applications requiring efficient voice-based capabilities.",
		"use_case":              "Voice assistants, real-time transcription, customer support, interactive voice applications, sentiment analysis, text-to-speech content creation",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    "Up to Oct 2023",
		"bias_data":             "{}",
	},
	"gpt-4o-mini-audio-preview": map[string]any{
		"description":           "This is a preview release of the smaller GPT-4o Audio mini model. GPT-4o Mini Audio is a lightweight, cost-effective multimodal model developed by OpenAI, capable of processing and generating both text and audio. It offers high-quality audio interactions at a fraction of the cost of the full GPT-4o audio models, making it ideal for applications requiring efficient voice-based capabilities.",
		"use_case":              "Voice assistants, real-time transcription, customer support, interactive voice applications, sentiment analysis, text-to-speech content creation",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    "Up to Oct 2023",
		"bias_data":             "{}",
	},
	"gpt-4o-mini-audio-preview-2024-12-17": map[string]any{
		"description":           "This is a preview release of the smaller GPT-4o Audio mini model. GPT-4o Mini Audio is a lightweight, cost-effective multimodal model developed by OpenAI, capable of processing and generating both text and audio. It offers high-quality audio interactions at a fraction of the cost of the full GPT-4o audio models, making it ideal for applications requiring efficient voice-based capabilities.",
		"use_case":              "Voice assistants, real-time transcription, customer support, interactive voice applications, sentiment analysis, text-to-speech content creation",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    "Up to Oct 2023",
		"bias_data":             "{}",
	},
	"gpt-image-1": map[string]any{
		"description":           "GPT-Image-1 is OpenAI's most advanced image generation model, built upon the GPT-4o architecture. It excels at producing high-resolution, detailed, and contextually accurate images from natural language prompts. The model supports text-to-image generation, image editing through inpainting, and precise text rendering within images.",
		"use_case":              "Creative content generation, product design, educational illustrations, marketing visuals, avatar creation, and applications requiring high-fidelity image synthesis.",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    "Up to Oct 2023",
		"bias_data":             "{}",
	},
	"gpt-4o-transcribe": map[string]any{
		"description":           "GPT-4o-transcribe is OpenAI's advanced speech-to-text model built upon the GPT-4o architecture. It offers high-accuracy transcription capabilities, supporting real-time streaming via WebSocket and batch processing. The model demonstrates improved word error rates and enhanced language recognition compared to previous models.",
		"use_case":              "Real-time transcription for meetings and live events, automated captioning, voice assistant integration, customer service call analysis, and transcription of audio content for accessibility.",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    "Up to Jun 2024",
		"bias_data":             "{}",
	},
	"gpt-4o-mini-transcribe": map[string]any{
		"description":           "GPT-4o-mini-transcribe is a lightweight, cost-effective speech-to-text model developed by OpenAI. Built upon the GPT-4o mini architecture, it offers real-time transcription capabilities with low latency, making it ideal for applications requiring quick and efficient audio-to-text conversion.",
		"use_case":              "Live captioning, voice-controlled applications, customer service call analysis, meeting transcription, and accessibility tools.",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    "Up to Jun 2024",
		"bias_data":             "{}",
	},
	"gpt-4o-mini-tts": map[string]any{
		"description":           "GPT-4o-mini-tts is OpenAI's advanced text-to-speech model built upon the GPT-4o mini architecture. It converts written text into natural, human-like speech with customizable voice attributes, supporting multiple languages and real-time streaming capabilities.",
		"use_case":              "Audiobook and podcast narration, language learning tools, accessibility enhancements, customer service voice responses, and real-time voice applications.",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    "Up to Oct 2023",
		"bias_data":             "{}",
	},
	"gpt-4o-2024-11-20": map[string]any{
		"description":           "GPT-4o-2024-11-20 is OpenAI's flagship multimodal model released on November 20, 2024. It natively processes and generates text, images, and audio, offering faster performance and improved conversational flow compared to its predecessors. The model supports a context window of 128,000 tokens and can generate up to 16,384 tokens per request.",
		"use_case":              "General-purpose AI tasks including text generation, image analysis, audio processing, multilingual translation, customer support automation, data analysis, and interactive applications requiring multimodal understanding.",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    "Up to Oct 2023",
		"bias_data":             "{}",
	},
	"gpt-4.1-mini": map[string]any{
		"description":           "GPT-4.1 Mini is OpenAI's compact yet powerful language model, released on April 14, 2025. It offers significant improvements over its predecessor, GPT-4o Mini, including enhanced instruction-following, coding capabilities, and long-context comprehension. With a context window of up to 1 million tokens and the ability to generate up to 32,768 tokens per request, GPT-4.1 Mini is optimized for efficiency and performance.",
		"use_case":              "Ideal for applications requiring fast and cost-effective language processing, such as real-time chatbots, code generation tools, educational platforms, and content creation services.",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    "Up to May 31, 2024",
		"bias_data":             "{}",
	},
	"gpt-4.1-mini-2025-04-14": map[string]any{
		"description":           "GPT-4.1 Mini is OpenAI's compact yet powerful language model, released on April 14, 2025. It offers significant improvements over its predecessor, GPT-4o Mini, including enhanced instruction-following, coding capabilities, and long-context comprehension. With a context window of up to 1 million tokens and the ability to generate up to 32,768 tokens per request, GPT-4.1 Mini is optimized for efficiency and performance.",
		"use_case":              "Ideal for applications requiring fast and cost-effective language processing, such as real-time chatbots, code generation tools, educational platforms, and content creation services.",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    "Up to May 31, 2024",
		"bias_data":             "{}",
	},
	"computer-use-preview": map[string]any{
		"description":           "The computer-use-preview model is OpenAI's experimental agent designed to interact with graphical user interfaces (GUIs) through natural language instructions. It combines vision capabilities with action execution, enabling it to perform tasks by interpreting visual elements and controlling a computer similarly to a human user.",
		"use_case":              "Automating web browsing, form filling, software navigation, and other GUI-based tasks through natural language commands.",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    "Up to May 2024",
		"bias_data":             "{}",
	},
	"computer-use-preview-2025-03-11": map[string]any{
		"description":           "The computer-use-preview model is OpenAI's experimental agent designed to interact with graphical user interfaces (GUIs) through natural language instructions. It combines vision capabilities with action execution, enabling it to perform tasks by interpreting visual elements and controlling a computer similarly to a human user.",
		"use_case":              "Automating web browsing, form filling, software navigation, and other GUI-based tasks through natural language commands.",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    "Up to May 2024",
		"bias_data":             "{}",
	},
	"o3": map[string]any{
		"description":           "OpenAI's o3 model, released on April 16, 2025, is a frontier reasoning model designed to excel in complex tasks requiring deep analysis. It demonstrates significant improvements over its predecessors in benchmarks like Codeforces, SWE-bench, and MMMU. The model is particularly strong in visual tasks, such as analyzing images, charts, and graphics.",
		"use_case":              "Advanced problem-solving in programming, scientific research, data analysis, and tasks necessitating multi-step reasoning.",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    "Up to May 2024",
		"bias_data":             "{}",
	},
	"o3-2025-04-16": map[string]any{
		"description":           "OpenAI's o3 model, released on April 16, 2025, is a frontier reasoning model designed to excel in complex tasks requiring deep analysis. It demonstrates significant improvements over its predecessors in benchmarks like Codeforces, SWE-bench, and MMMU. The model is particularly strong in visual tasks, such as analyzing images, charts, and graphics.",
		"use_case":              "Advanced problem-solving in programming, scientific research, data analysis, and tasks necessitating multi-step reasoning.",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    "Up to May 2024",
		"bias_data":             "{}",
	},
	"o3-pro-2025-06-10": map[string]any{
		"description":           "OpenAI's o3-pro model, released on June 10, 2025, is an advanced variant of the o3 frontier reasoning model. It builds upon o3 with enhanced performance in complex reasoning, multi-modal understanding, and long-context tasks. It offers state-of-the-art results in domains such as software engineering, scientific analysis, and high-stakes decision-making.",
		"use_case":              "High-performance use cases requiring long-context comprehension, precise reasoning across modalities (text, image, code), and advanced analysis — including enterprise-grade AI applications and research.",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"training_till_date":    "Up to May 2024",
		"suggested_replacement": nil,
		"bias_data":             "{}",
	},
	"o3-pro": map[string]any{
		"description":           "Alias for OpenAI's o3-pro-2025-06-10 model. It offers cutting-edge capabilities for reasoning, multi-modal input, and long-context tasks, optimized for premium and enterprise-level applications.",
		"use_case":              "Advanced reasoning, software development, data science, document analysis, and multi-modal problem solving with extended context.",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"training_till_date":    "Up to May 2024",
		"suggested_replacement": nil,
		"bias_data":             "{}",
	},
	"o4-mini-deep-research-2025-06-26": map[string]any{
		"description":           "An advanced variant of OpenAI's o4-mini model, optimized for deep research and analytical reasoning across text and visual data.",
		"use_case":              "Scientific literature review, market analysis, academic research, investigative reporting",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    "Up to Jun 2025",
		"bias_data":             "{}",
	},
	"o4-mini-deep-research": map[string]any{
		"description":           "An advanced variant of OpenAI's o4-mini model, optimized for deep research and analytical reasoning across text and visual data.",
		"use_case":              "Scientific literature review, market analysis, academic research, investigative reporting",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    "Up to Jun 2025",
		"bias_data":             "{}",
	},
	"o3-deep-research-2025-06-26": map[string]any{
		"description":           "A high-efficiency research model from OpenAI’s o3 series, suited for deep analytical tasks with long-form textual data.",
		"use_case":              "Technical documentation parsing, competitive intelligence, compliance research",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    "Up to Jun 2025",
		"bias_data":             "{}",
	},
	"o3-deep-research": map[string]any{
		"description":           "A high-efficiency research model from OpenAI’s o3 series, suited for deep analytical tasks with long-form textual data.",
		"use_case":              "Technical documentation parsing, competitive intelligence, compliance research",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    "Up to Jun 2025",
		"bias_data":             "{}",
	},
	"gpt-5-nano": map[string]any{
		"description":           "GPT-5 Nano is OpenAI’s fastest, most cost-efficient reasoning model, optimized for ultra-low latency and speed. Suitable for instant summarization and classification, it supports both text and image input. It features a 272,000-token context window and generates up to 128,000 tokens per request.",
		"use_case":              "Ideal for applications demanding ultra-fast response, such as real-time analytics, low-latency chatbots, instant summarization, light agentic tasks, and large-scale classification.",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    "Up to May 31, 2024",
		"bias_data":             "{}",
	},
	"gpt-5": map[string]any{
		"description":           "GPT-5 is OpenAI’s flagship for coding, reasoning, and agentic tasks, unifying advanced logic, multimodal input, and execution into a single model. It supports text, images, functions, and parallel tool use, offering deep reasoning and chain-of-thought processing. Context window is 272,000 tokens and max output is 128,000 tokens.",
		"use_case":              "Best for complex workflows, advanced coding, data analysis, multimodal content generation, enterprise-grade chatbots, research, and agent-style automation.",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    "Up to October 24, 2024",
		"bias_data":             "{}",
	},
	"gpt-5-mini-2025-08-07": map[string]any{
		"description":           "GPT-5 Mini (2025-08-07) is a lightweight version of GPT-5, built for cost-sensitive deployments and faster execution. Retains instruction-following and robust reasoning, supports text and image input, with a 272,000-token context window and up to 128,000 tokens per output.",
		"use_case":              "Great for scalable chat services, moderately complex automation, educational or creative platforms, and budget-conscious code generation.",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    "Up to June 24, 2024",
		"bias_data":             "{}",
	},
	"gpt-5-mini": map[string]any{
		"description":           "GPT-5 Mini is a compact, faster, and more cost-efficient GPT-5 variant for well-defined tasks and precise prompts. It supports both text and image input, with high reasoning, a 272,000-token context window, and up to 128,000 output tokens.",
		"use_case":              "Optimized for fast chatbots, precise code generation, educational apps, and scalable content creation.",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    "Up to June 24, 2024",
		"bias_data":             "{}",
	},
	"gpt-5-nano-2025-08-07": map[string]any{
		"description":           "GPT-5 Nano (2025-08-07) is the most efficient GPT-5 release, offering ultra-fast, low-cost reasoning for instant and lightweight tasks. It works with text and image inputs, with a 272,000-token context window and 128,000 output tokens.",
		"use_case":              "Ideal for lightweight analytics, summarization, classification, and low-latency interactive agents.",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    "Up to May 31, 2024",
		"bias_data":             "{}",
	},
	"gpt-5-chat-latest": map[string]any{
		"description":           "GPT-5 Chat (latest) is optimized for advanced, natural, and multimodal conversations in enterprise chat apps. It is text-only with a 128,000-token context window and outputs up to 16,384 tokens.",
		"use_case":              "Best for scalable enterprise chatbots, customer support, multilingual conversations, and context-heavy dialogue.",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    "Up to October 24, 2024",
		"bias_data":             "{}",
	},
	"gpt-5-2025-08-07": map[string]any{
		"description":           "GPT-5 (2025-08-07) is the latest release of OpenAI’s frontier model for reasoning and coding, supporting multimodal inputs and agentic workflows. It maintains a 272,000-token context window and up to 128,000 output tokens.",
		"use_case":              "Best for all advanced use cases requiring deep reasoning, code collaboration, and complex workflow automation on August 07, 2025.",
		"deprecation_date":      nil,
		"legacy_model":          false,
		"retirement_date":       nil,
		"suggested_replacement": nil,
		"training_till_date":    "Up to October 24, 2024",
		"bias_data":             "{}",
	},
}

var StaticModelsBlockedStatus = map[string]bool{
	"gpt-4o-realtime-preview-2024-12-17":      true,
	"gpt-4o-audio-preview-2024-12-17":         true,
	"gpt-4o-audio-preview-2025-06-03":         true,
	"gpt-4o-realtime-preview-2025-06-03":      true,
	"gpt-4-1106-preview":                      true,
	"dall-e-3":                                true,
	"dall-e-2":                                true,
	"gpt-4o-audio-preview-2024-10-01":         true,
	"gpt-4-turbo-preview":                     true,
	"text-embedding-3-small":                  true,
	"babbage-002":                             true,
	"gpt-4":                                   true,
	"text-embedding-ada-002":                  true,
	"chatgpt-4o-latest":                       true,
	"gpt-4o-mini-audio-preview":               true,
	"gpt-4o-audio-preview":                    true,
	"o1-preview-2024-09-12":                   true,
	"gpt-4o-mini-realtime-preview":            true,
	"gpt-4o-mini-realtime-preview-2024-12-17": true,
	"gpt-4.1-nano":                            true,
	"gpt-3.5-turbo-instruct-0914":             true,
	"gpt-4o-mini-search-preview":              true,
	"gpt-4.1-nano-2025-04-14":                 true,
	"gpt-3.5-turbo-16k":                       true,
	"gpt-4o-realtime-preview":                 true,
	"davinci-002":                             true,
	"gpt-3.5-turbo-1106":                      true,
	"gpt-4o-search-preview":                   true,
	"gpt-3.5-turbo-instruct":                  true,
	"gpt-3.5-turbo":                           true,
	"o3-mini-2025-01-31":                      true,
	"gpt-4o-mini-search-preview-2025-03-11":   true,
	"gpt-4-0125-preview":                      true,
	"gpt-4o-2024-11-20":                       true,
	"gpt-4o-2024-05-13":                       true,
	"text-embedding-3-large":                  true,
	"o1-2024-12-17":                           true,
	"o1":                                      true,
	"o1-preview":                              true,
	"gpt-4-0613":                              true,
	"o1-mini":                                 true,
	"gpt-4o-mini-tts":                         true,
	"o1-pro":                                  true,
	"gpt-4o-transcribe":                       true,
	"gpt-4.5-preview":                         true,
	"o1-pro-2025-03-19":                       true,
	"gpt-4.5-preview-2025-02-27":              true,
	"gpt-4o-search-preview-2025-03-11":        true,
	"omni-moderation-2024-09-26":              true,
	"gpt-image-1":                             true,
	"o1-mini-2024-09-12":                      true,
	"tts-1-hd":                                true,
	"gpt-4o":                                  true,
	"tts-1-hd-1106":                           true,
	"gpt-4o-2024-08-06":                       true,
	"gpt-4o-mini-2024-07-18":                  true,
	"gpt-4.1-mini":                            true,
	"gpt-4o-mini":                             true,
	"gpt-4o-mini-audio-preview-2024-12-17":    true,
	"gpt-3.5-turbo-0125":                      true,
	"gpt-4-turbo":                             true,
	"tts-1":                                   true,
	"gpt-4-turbo-2024-04-09":                  true,
	"tts-1-1106":                              true,
	"gpt-4o-realtime-preview-2024-10-01":      true,
	"gpt-4o-mini-transcribe":                  true,
	"gpt-4.1-mini-2025-04-14":                 true,
	"o3-mini":                                 true,
	"gpt-4.1":                                 true,
	"whisper-1":                               true,
	"gpt-4.1-2025-04-14":                      true,
	"omni-moderation-latest":                  true,
	"o4-mini-2025-04-16":                      true,
	"o4-mini":                                 true,
	"codex-mini-latest":                       true,
	"computer-use-preview":                    true,
	"computer-use-preview-2025-03-11":         true,
	"o3":                                      true,
	"o3-2025-04-16":                           true,
	"o3-pro-2025-06-10":                       true,
	"o3-pro":                                  true,
	"o4-mini-deep-research-2025-06-26":        true,
	"o4-mini-deep-research":                   true,
	"o3-deep-research":                        true,
	"o3-deep-research-2025-06-26":             true,
	"gpt-5-nano":                              true,
	"gpt-5":                                   true,
	"gpt-5-mini-2025-08-07":                   true,
	"gpt-5-mini":                              true,
	"gpt-5-nano-2025-08-07":                   true,
	"gpt-5-chat-latest":                       true,
	"gpt-5-2025-08-07":                        true,
}

var CompletionsCost = map[string]map[string]float64{
	"gpt-4.1-2025-04-14": {
		"input":        2.0,
		"cached_input": 0.50,
		"output":       8.0,
	},
	"gpt-4.1-mini-2025-04-14": {
		"input":        0.40,
		"cached_input": 0.10,
		"output":       1.60,
	},
	"gpt-4.1-nano-2025-04-14": {
		"input":        0.10,
		"cached_input": 0.025,
		"output":       0.40,
	},
	"gpt-4.5-preview-2025-02-27": {
		"input":        75.0,
		"cached_input": 37.5,
		"output":       150.0,
	},
	"gpt-4o": {
		"input":        2.5,
		"cached_input": 1.25,
		"output":       10.0,
	},
	"gpt-4o-audio-preview-2024-12-17": {
		"input":        2.50,
		"cached_input": 0,
		"output":       10,
	},
	"gpt-4o-realtime-preview-2024-12-17": {
		"input":        5.0,
		"cached_input": 2.5,
		"output":       20.0,
	},
	"gpt-4o-mini-audio-preview-2024-12-17": {
		"input":        0.15,
		"cached_input": 0,
		"output":       0.60,
	},
	"gpt-4o-mini-realtime-preview-2024-12-17": {
		"input":        0.6,
		"cached_input": 0.3,
		"output":       2.4,
	},
	"gpt-4o-mini-2024-07-18": {
		"input":        0.15,
		"cached_input": 0.075,
		"output":       0.60,
	},
	"o1": {
		"input":        15.0,
		"cached_input": 7.5,
		"output":       60.0,
	},
	"o1-mini": {
		"input":        1.1,
		"cached_input": 0.55,
		"output":       4.4,
	},
	"o3-mini": {
		"input":        1.1,
		"cached_input": 0.55,
		"output":       4.4,
	},
	"gpt-4 turbo": {
		"input":        10.0,
		"cached_input": 0.0,
		"output":       30.0,
	},
	"gpt-4": {
		"input":        30.0,
		"cached_input": 0.0,
		"output":       60.0,
	},
	"gpt-3.5 turbo": {
		"input":        0.5,
		"cached_input": 0.0,
		"output":       1.5,
	},
	"o1-2024-12-17": {
		"input":        15.0,
		"cached_input": 7.5,
		"output":       60.0,
	},
	"gpt-4-0125-preview": {
		"input":        10,
		"cached_input": 0,
		"output":       30,
	},
	"o3-mini-2025-01-31": {
		"input":        1.10,
		"cached_input": 0.55,
		"output":       4.40,
	},
	"gpt-4o-2024-08-06": {
		"input":        2.5,
		"cached_input": 1.25,
		"output":       10,
	},
	"chatgpt-4o-latest": {
		"input":  5.00,
		"output": 15.00,
	},
	"gpt-4-turbo-2024-04-09": {
		"input":  10.00,
		"output": 30.00,
	},
	"gpt-4-0613": {
		"input":  30.00,
		"output": 60.00,
	},
	"gpt-4-32k": {
		"input":  60.00,
		"output": 120.00,
	},
	"gpt-3.5-turbo-0125": {
		"input":  0.50,
		"output": 1.50,
	},
	"gpt-3.5-turbo-instruct": {
		"input":  1.50,
		"output": 2.00,
	},
	"gpt-3.5-turbo-16k-0613": {
		"input":  3.00,
		"output": 4.00,
	},
	"davinci-002": {
		"input":  2.00,
		"output": 2.00,
	},
	"babbage-002": {
		"input":  0.40,
		"output": 0.40,
	},
	"gpt-4.1": {
		"input":        2.00,
		"cached_input": 0.50,
		"output":       8.00,
	},
	"gpt-4.1-mini": {
		"input":        0.40,
		"cached_input": 0.10,
		"output":       1.60,
	},
	"gpt-4.1-nano": {
		"input":        0.10,
		"cached_input": 0.025,
		"output":       0.40,
	},
	"gpt-4.5-preview": {
		"input":        75.00,
		"cached_input": 37.50,
		"output":       150.00,
	},
	"gpt-4o-2024-11-20": {
		"input":        2.50,
		"cached_input": 1.25,
		"output":       10.00,
	},
	"gpt-4o-2024-05-13": {
		"input":        5.00,
		"cached_input": 0.00,
		"output":       15.00,
	},
	"gpt-4o-audio-preview": {
		"input":        2.50,
		"cached_input": 0.00,
		"output":       10.00,
	},
	"gpt-4o-audio-preview-2025-06-03": {
		"input":        2.50,
		"cached_input": 0.00,
		"output":       10.00,
	},
	"gpt-4o-audio-preview-2024-10-01": {
		"input":        2.50,
		"cached_input": 0.00,
		"output":       10.00,
	},
	"gpt-4o-realtime-preview": {
		"input":        5.00,
		"cached_input": 2.50,
		"output":       20.00,
	},
	"gpt-4o-realtime-preview-2025-06-03": {
		"input":        5.00,
		"cached_input": 2.50,
		"output":       20.00,
	},
	"gpt-4o-realtime-preview-2024-10-01": {
		"input":        5.00,
		"cached_input": 2.50,
		"output":       20.00,
	},
	"gpt-4o-mini": {
		"input":        0.15,
		"cached_input": 0.075,
		"output":       0.60,
	},
	"gpt-4o-mini-audio-preview": {
		"input":        0.15,
		"cached_input": 0.00,
		"output":       0.60,
	},
	"gpt-4o-mini-realtime-preview": {
		"input":        0.60,
		"cached_input": 0.30,
		"output":       2.40,
	},
	"gpt-4o-mini-search-preview": {
		"input":        0.15,
		"cached_input": 0.00,
		"output":       0.60,
	},
	"gpt-4o-search-preview": {
		"input":        2.50,
		"cached_input": 0.00,
		"output":       10.00,
	},
	"gpt-image-1": {
		"input":        5.00,
		"cached_input": 1.25,
		"output":       0.00,
	},
	"o1-preview-2024-09-12": {
		"input":        15.00,
		"cached_input": 7.50,
		"output":       60.00,
	},
	"o1-pro": {
		"input":        150.00,
		"cached_input": 0.00,
		"output":       600.00,
	},
	"o3": {
		"input":        2.00,
		"cached_input": 0.50,
		"output":       8.00,
	},
	"o3-2025-04-16": {
		"input":        2.00,
		"cached_input": 0.50,
		"output":       8.00,
	},
	"o3-deep-research": {
		"input":        10.00,
		"cached_input": 2.50,
		"output":       40.00,
	},
	"o4-mini": {
		"input":        1.10,
		"cached_input": 0.275,
		"output":       4.40,
	},
	"o4-mini-deep-research": {
		"input":        2.00,
		"cached_input": 0.50,
		"output":       8.00,
	},
	"codex-mini-latest": {
		"input":        1.50,
		"cached_input": 0.375,
		"output":       6.00,
	},
	"computer-use-preview": {
		"input":        3.00,
		"cached_input": 0.00,
		"output":       12.00,
	},
	"gpt-5-nano": {
		"input":        0.05,
		"cached_input": 0.005,
		"output":       0.40,
	},
	"gpt-5": {
		"input":        1.25,
		"cached_input": 0.125,
		"output":       10.0,
	},
	"gpt-5-mini": {
		"input":        0.25,
		"cached_input": 0.025,
		"output":       2.0,
	},
	"gpt-5-nano-2025-08-07": {
		"input":        0.05,
		"cached_input": 0.005,
		"output":       0.40,
	},
	"gpt-5-2025-08-07": {
		"input":        1.25,
		"cached_input": 0.125,
		"output":       10.0,
	},
	"gpt-5-mini-2025-08-07": {
		"input":        0.25,
		"cached_input": 0.025,
		"output":       2.0,
	},
	"gpt-5-chat-latest": {
		"input":        1.25,
		"cached_input": 0.125,
		"output":       10.00,
	},
}
